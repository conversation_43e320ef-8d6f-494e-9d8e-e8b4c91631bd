# Expo Usage Guide

## 🎉 Installation Complete!

Your Expo expedition document processor is now installed and working! Here's how to use it:

## 🚀 Quick Start

### 1. Activate the Environment
```bash
cd /Users/<USER>/Desktop/Projects/File/Expo
source expo_env/bin/activate
```

### 2. Run the Demo (Optional)
```bash
python demo.py
```

### 3. Use Expo

#### GUI Interface (Recommended)
```bash
python main.py
```
This opens a file picker where you can:
- Select multiple expedition documents (PDF, DOCX, DOC, TXT)
- See processing progress
- Automatically save JSON outputs

#### CLI Interface
```bash
# Process single file
python cli.py document.pdf

# Process multiple files
python cli.py doc1.pdf doc2.docx doc3.txt

# Custom output directory
python cli.py document.pdf -o ~/custom_output/

# Verbose logging
python cli.py document.pdf -v
```

## 📁 Output

JSON files are automatically saved to:
```
~/Desktop/output/
```

With naming convention:
- `location-date.json` (e.g., `paradise-bay-2024-07-13.json`)

## 📊 What You Get

Each processed document generates a structured JSON file like:

```json
{
  "dayNumber": 1,
  "weekday": "Saturday",
  "date": "2024-07-13",
  "location": "Paradise Bay",
  "utcOffset": "+00:00",
  "notes": "Equipment and setup notes",
  "zodiacs": 6,
  "twins": 2,
  "activityType": "Full Day Landing",
  "groups": [
    {
      "groupName": "Yellow",
      "color": "Yellow",
      "departureTime": "08:00",
      "returnTime": "18:00",
      "activity": "Landing"
    }
  ],
  "schedule": [
    {
      "time": "08:00",
      "type": "departure",
      "description": "Yellow Group departure",
      "location": "Paradise Bay"
    }
  ],
  "tides": [
    {
      "time": "03:30",
      "height": 1.8,
      "label": "Low Tide"
    }
  ],
  "groupOrder": ["Yellow", "Blue", "Red", "Green"],
  "zodiacDrivers": [],
  "landingGuides": {
    "wave1Guides": [],
    "wave2Guides": []
  },
  "nextDayLocation": "",
  "sunTimes": {
    "twilightStart": "",
    "sunrise": "",
    "sunset": "",
    "twilightEnd": ""
  }
}
```

## 🔧 Features

- ✅ **Document Processing**: PDF, DOCX, DOC, TXT support
- ✅ **Intelligent Extraction**: LangChain agents with Ollama
- ✅ **Fallback Processing**: Works even without LangChain
- ✅ **Structured Output**: Consistent JSON templates
- ✅ **Multiple Interfaces**: GUI and CLI options
- ✅ **Smart Naming**: Automatic file naming with location-date
- ✅ **Error Handling**: Graceful error recovery

## 🛠️ Troubleshooting

### If Ollama isn't working:
```bash
ollama serve
```

### If you get import errors:
```bash
source expo_env/bin/activate
```

### To reinstall:
```bash
./install.sh
```

## 📝 Next Steps

1. **Process your own documents**: Drop expedition PDFs/DOCX files into the GUI
2. **Check outputs**: Review generated JSON files in `~/Desktop/output/`
3. **Customize**: Modify extraction patterns in `tools/extraction_tools.py`
4. **Integrate**: Use the JSON outputs in your expedition planning workflow

## 🎯 Success!

You now have a fully functional expedition document processor that:
- Extracts structured data from expedition documents
- Generates consistent JSON templates
- Works with multiple file formats
- Has both GUI and CLI interfaces
- Integrates with your preferred Ollama model

Happy expedition planning! 🗺️⛵
