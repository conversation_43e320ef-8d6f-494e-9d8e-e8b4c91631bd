#!/usr/bin/env python3
"""
Simple test for the extraction agent only.
"""

import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_agent():
    """Test the extraction agent directly."""
    try:
        from agents.extraction_agent import ExtractionAgent
        
        print("🔧 Testing Extraction Agent...")
        
        # Initialize agent
        agent = ExtractionAgent(timeout=30, max_iterations=5)
        
        # Check if agent is available
        if not agent.is_available():
            print("❌ Agent not available")
            return False
        
        print("✅ Agent initialized successfully")
        
        # Test document
        test_doc = """
        EXPEDITION PLANNING DOCUMENT
        
        Location: Test Island
        Date: 2024-07-15
        Day: Monday
        
        Equipment:
        - 6 Zodiac boats
        - 2 Twin boats
        
        Schedule:
        08:00 - Yellow Group departure
        10:00 - Blue Group departure
        
        Tides:
        03:00 - Low Tide (1.5m)
        15:00 - High Tide (4.0m)
        """
        
        print("📄 Testing extraction...")
        
        # Extract entities
        result = agent.extract_entities(test_doc)
        
        print("📊 Results:")
        print(f"  Location: {result.get('location', 'N/A')}")
        print(f"  Date: {result.get('date', 'N/A')}")
        print(f"  Weekday: {result.get('weekday', 'N/A')}")
        print(f"  Zodiacs: {result.get('zodiacs', 'N/A')}")
        print(f"  Twins: {result.get('twins', 'N/A')}")
        
        # Check if we got meaningful results
        if result.get('location') and result.get('date'):
            print("✅ Agent extraction successful!")
            return True
        else:
            print("❌ Agent extraction failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_agent()
    sys.exit(0 if success else 1)
