"""
Ollama and LLM configuration for Expo.
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional


@dataclass
class OllamaConfig:
    """Configuration for Ollama LLM integration."""
    
    # Ollama connection
    base_url: str = "http://localhost:11434"
    model_name: str = "mistral-7b-v0-1-gguf:latest"  # User's preferred model
    
    # Model parameters
    temperature: float = 0.1
    top_p: float = 0.9
    max_tokens: int = 2048
    
    # Request settings
    timeout_seconds: int = 120
    max_retries: int = 3
    
    # Agent-specific settings
    agent_max_iterations: int = 10
    agent_early_stopping_method: str = "generate"
    
    def get_model_kwargs(self) -> Dict[str, Any]:
        """Get model parameters as kwargs."""
        return {
            "temperature": self.temperature,
            "top_p": self.top_p,
            "num_predict": self.max_tokens,
        }
    
    def get_agent_kwargs(self) -> Dict[str, Any]:
        """Get agent parameters as kwargs."""
        return {
            "max_iterations": self.agent_max_iterations,
            "early_stopping_method": self.agent_early_stopping_method,
        }
    
    def validate_connection(self) -> bool:
        """Validate Ollama connection (placeholder)."""
        # TODO: Implement actual connection test
        return True


# Global Ollama configuration
ollama_config = OllamaConfig()
