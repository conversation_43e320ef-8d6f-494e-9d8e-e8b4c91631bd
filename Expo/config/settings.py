"""
Expo application settings and configuration.
"""

import os
from pathlib import Path
from typing import List, Optional
from dataclasses import dataclass


@dataclass
class ExpoSettings:
    """Main configuration settings for Expo."""
    
    # Application info
    app_name: str = "Expo"
    version: str = "1.0.0"
    
    # File processing
    supported_extensions: List[str] = None
    max_file_size_mb: int = 100
    
    # Output settings
    output_directory: str = "~/Desktop/output"
    json_indent: int = 2
    
    # Processing settings
    enable_ocr: bool = True
    enable_validation: bool = True
    
    # LangChain/Agent settings
    agent_timeout_seconds: int = 300
    max_retries: int = 3
    
    def __post_init__(self):
        """Initialize default values after creation."""
        if self.supported_extensions is None:
            self.supported_extensions = ['.pdf', '.docx', '.doc', '.txt']
        
        # Expand user path
        self.output_directory = os.path.expanduser(self.output_directory)
        
        # Ensure output directory exists
        Path(self.output_directory).mkdir(parents=True, exist_ok=True)
    
    @property
    def output_path(self) -> Path:
        """Get output directory as Path object."""
        return Path(self.output_directory)
    
    def is_supported_file(self, file_path: str) -> bool:
        """Check if file extension is supported."""
        return Path(file_path).suffix.lower() in self.supported_extensions


# Global settings instance
settings = ExpoSettings()
