#!/usr/bin/env python3
"""
Simple GUI for Expo that avoids macOS tkinter file dialog issues.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
from pathlib import Path
import threading
import sys
import logging

# Add Expo to path
sys.path.insert(0, str(Path(__file__).parent))

from main import ExpoApp

logger = logging.getLogger(__name__)


class SimpleExpoGUI:
    """Simple drag-and-drop style GUI for Expo."""
    
    def __init__(self):
        """Initialize the simple GUI."""
        self.root = tk.Tk()
        self.root.title("Expo - Expedition Document Processor")
        self.root.geometry("600x500")
        
        # Initialize Expo app
        self.expo_app = ExpoApp()
        
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the user interface."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(
            main_frame,
            text="Expo - Expedition Document Processor",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Instructions
        instructions = ttk.Label(
            main_frame,
            text="Enter file paths below (one per line) or use the Browse button:",
            font=("Arial", 10)
        )
        instructions.pack(pady=(0, 10))
        
        # File input area
        input_frame = ttk.LabelFrame(main_frame, text="Document Files", padding="10")
        input_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # Text area for file paths
        self.file_text = tk.Text(
            input_frame,
            height=10,
            wrap=tk.WORD,
            font=("Monaco", 10)
        )
        self.file_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Add some example text
        example_text = """# Enter file paths here, one per line:
# Example:
# /Users/<USER>/Documents/expedition1.pdf
# /Users/<USER>/Documents/expedition2.docx
# /Users/<USER>/Documents/expedition3.txt

# Or use the Browse button below to add files
"""
        self.file_text.insert("1.0", example_text)
        
        # Buttons frame
        button_frame = ttk.Frame(input_frame)
        button_frame.pack(fill=tk.X)
        
        # Browse button
        browse_btn = ttk.Button(
            button_frame,
            text="Browse Files",
            command=self.browse_files
        )
        browse_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Clear button
        clear_btn = ttk.Button(
            button_frame,
            text="Clear",
            command=self.clear_files
        )
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Process button
        process_btn = ttk.Button(
            button_frame,
            text="Process Documents",
            command=self.process_documents,
            style="Accent.TButton"
        )
        process_btn.pack(side=tk.RIGHT)
        
        # Status area
        status_frame = ttk.LabelFrame(main_frame, text="Status", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Progress bar
        self.progress = ttk.Progressbar(
            status_frame,
            mode='indeterminate'
        )
        self.progress.pack(fill=tk.X, pady=(0, 5))
        
        # Status label
        self.status_label = ttk.Label(
            status_frame,
            text="Ready to process documents"
        )
        self.status_label.pack()
        
        # Output info
        output_frame = ttk.LabelFrame(main_frame, text="Output", padding="10")
        output_frame.pack(fill=tk.X)
        
        output_info = ttk.Label(
            output_frame,
            text=f"JSON files will be saved to: ~/Desktop/output/",
            font=("Arial", 9),
            foreground="gray"
        )
        output_info.pack()
    
    def browse_files(self):
        """Browse for files using native file dialog."""
        try:
            from ui.file_picker import FilePicker

            # Create file picker
            picker = FilePicker()

            # Select files
            selected_files = picker.select_files()

            if selected_files:
                # Clear existing text
                self.file_text.delete("1.0", tk.END)

                # Add selected files to text area
                file_paths = "\n".join(selected_files)
                self.file_text.insert("1.0", file_paths)

                # Show success message
                messagebox.showinfo(
                    "Files Selected",
                    f"Selected {len(selected_files)} file(s):\n\n" +
                    "\n".join([Path(f).name for f in selected_files[:5]]) +
                    (f"\n... and {len(selected_files) - 5} more" if len(selected_files) > 5 else "")
                )

        except Exception as e:
            logger.error(f"Browse files error: {e}")
            messagebox.showerror("Error", f"Failed to browse files: {e}")
    
    def clear_files(self):
        """Clear the file input area."""
        self.file_text.delete("1.0", tk.END)
    
    def get_file_paths(self):
        """Get file paths from the text area."""
        content = self.file_text.get("1.0", tk.END)
        lines = content.strip().split('\n')
        
        file_paths = []
        for line in lines:
            line = line.strip()
            # Skip comments and empty lines
            if line and not line.startswith('#'):
                # Expand user path
                path = os.path.expanduser(line)
                if os.path.exists(path):
                    file_paths.append(path)
                else:
                    print(f"Warning: File not found: {path}")
        
        return file_paths
    
    def process_documents(self):
        """Process the documents."""
        file_paths = self.get_file_paths()
        
        if not file_paths:
            messagebox.showwarning(
                "No Files",
                "Please enter some file paths in the text area."
            )
            return
        
        # Validate files
        valid_files = []
        invalid_files = []
        
        for path in file_paths:
            if Path(path).suffix.lower() in ['.pdf', '.docx', '.doc', '.txt']:
                valid_files.append(path)
            else:
                invalid_files.append(path)
        
        if invalid_files:
            messagebox.showwarning(
                "Unsupported Files",
                f"These files are not supported:\n" + 
                "\n".join([Path(f).name for f in invalid_files])
            )
        
        if not valid_files:
            messagebox.showerror("No Valid Files", "No supported files found.")
            return
        
        # Start processing in background
        self.start_processing(valid_files)
    
    def start_processing(self, file_paths):
        """Start processing files in background thread."""
        self.progress.start()
        self.status_label.config(text=f"Processing {len(file_paths)} files...")
        
        # Disable process button
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, ttk.Button) and "Process" in child.cget("text"):
                        child.config(state="disabled")
        
        # Start processing thread
        thread = threading.Thread(
            target=self.process_files_thread,
            args=(file_paths,)
        )
        thread.daemon = True
        thread.start()
    
    def process_files_thread(self, file_paths):
        """Process files in background thread."""
        try:
            results = []
            
            for i, file_path in enumerate(file_paths):
                # Update status
                self.root.after(0, lambda: self.status_label.config(
                    text=f"Processing {i+1}/{len(file_paths)}: {Path(file_path).name}"
                ))
                
                # Process file
                result = self.expo_app.process_single_file(file_path)
                if result:
                    results.append(result)
            
            # Complete
            self.root.after(0, lambda: self.processing_complete(results, len(file_paths)))
            
        except Exception as e:
            self.root.after(0, lambda: self.processing_error(str(e)))
    
    def processing_complete(self, results, total_files):
        """Handle processing completion."""
        self.progress.stop()
        
        # Re-enable process button
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, ttk.Button) and "Process" in child.cget("text"):
                        child.config(state="normal")
        
        if results:
            self.status_label.config(
                text=f"✅ Completed! Generated {len(results)}/{total_files} JSON files"
            )
            
            # Show results
            result_names = [Path(r).name for r in results]
            messagebox.showinfo(
                "Processing Complete",
                f"Successfully processed {len(results)} files:\n\n" +
                "\n".join(result_names[:5]) +
                (f"\n... and {len(results)-5} more" if len(results) > 5 else "") +
                f"\n\nOutput directory: ~/Desktop/output/"
            )
        else:
            self.status_label.config(text="❌ No files were processed successfully")
            messagebox.showerror(
                "Processing Failed",
                "No files were processed successfully. Check the console for errors."
            )
    
    def processing_error(self, error_msg):
        """Handle processing error."""
        self.progress.stop()
        self.status_label.config(text=f"❌ Error: {error_msg}")
        
        # Re-enable process button
        for widget in self.root.winfo_children():
            if isinstance(widget, ttk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, ttk.Button) and "Process" in child.cget("text"):
                        child.config(state="normal")
        
        messagebox.showerror("Processing Error", f"Error: {error_msg}")
    
    def run(self):
        """Run the GUI."""
        self.root.mainloop()


def main():
    """Main entry point."""
    try:
        app = SimpleExpoGUI()
        app.run()
    except Exception as e:
        print(f"GUI Error: {e}")
        print("Try using the CLI instead: python cli.py document.pdf")


if __name__ == "__main__":
    main()
