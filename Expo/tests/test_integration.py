"""
Integration tests for Expo end-to-end functionality.
"""

import unittest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch

# Import Expo components
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from main import ExpoApp


class TestExpoIntegration(unittest.TestCase):
    """Test end-to-end Expo functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.app = ExpoApp()
        
        # Override output directory for testing
        self.app.json_generator.template_schema = self.app.json_generator._get_template_schema()
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_text_file_processing(self):
        """Test processing a simple text file."""
        # Create test text file
        test_content = """
        Expedition Planning Document
        
        Location: Test Island
        Date: 2024-07-12
        Day: Friday
        
        Equipment:
        - 8 Zodiac boats
        - 1 Twin boat
        
        Schedule:
        07:40 - Yellow Group departure
        09:40 - Blue Group departure
        
        Tides:
        02:00 - Low Tide (2.0m)
        14:30 - High Tide (4.5m)
        """
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            temp_file = f.name
        
        try:
            # Process the file
            result = self.app.process_single_file(temp_file)
            
            # Verify result
            self.assertIsNotNone(result)
            self.assertTrue(Path(result).exists())
            
            # Check JSON content
            with open(result, 'r') as f:
                json_data = json.load(f)
            
            # Verify extracted data
            self.assertIn("location", json_data)
            self.assertIn("date", json_data)
            self.assertIn("weekday", json_data)
            self.assertIn("zodiacs", json_data)
            self.assertIn("twins", json_data)
            
        finally:
            Path(temp_file).unlink()
            if result and Path(result).exists():
                Path(result).unlink()
    
    @patch('Expo.agents.extraction_agent.LANGCHAIN_AVAILABLE', False)
    def test_fallback_processing(self):
        """Test processing with LangChain unavailable (fallback mode)."""
        test_content = """
        Location: Fallback Island
        Date: 2024-07-13
        8 zodiac boats
        1 twin boat
        """
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            temp_file = f.name
        
        try:
            # Process with fallback
            result = self.app.process_single_file(temp_file)
            
            self.assertIsNotNone(result)
            
            # Verify fallback extraction worked
            with open(result, 'r') as f:
                json_data = json.load(f)
            
            self.assertEqual(json_data["zodiacs"], 8)
            self.assertEqual(json_data["twins"], 1)
            
        finally:
            Path(temp_file).unlink()
            if result and Path(result).exists():
                Path(result).unlink()
    
    def test_multiple_file_processing(self):
        """Test processing multiple files."""
        files = []
        
        # Create multiple test files
        for i in range(3):
            content = f"""
            Location: Test Location {i+1}
            Date: 2024-07-{12+i:02d}
            {i+5} zodiac boats
            """
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
                f.write(content)
                files.append(f.name)
        
        try:
            # Process all files
            results = self.app.process_files(files)
            
            # Verify results
            self.assertEqual(len(results), 3)
            
            for result in results:
                self.assertTrue(Path(result).exists())
                
                with open(result, 'r') as f:
                    json_data = json.load(f)
                
                self.assertIn("location", json_data)
                self.assertIn("date", json_data)
            
        finally:
            # Clean up
            for file_path in files:
                Path(file_path).unlink()
            for result in results:
                if Path(result).exists():
                    Path(result).unlink()
    
    def test_invalid_file_handling(self):
        """Test handling of invalid files."""
        # Create invalid file
        with tempfile.NamedTemporaryFile(suffix='.xyz', delete=False) as f:
            f.write(b"invalid content")
            invalid_file = f.name
        
        try:
            # Should handle gracefully
            result = self.app.process_single_file(invalid_file)
            self.assertIsNone(result)
            
        finally:
            Path(invalid_file).unlink()


class TestExpoTools(unittest.TestCase):
    """Test Expo extraction tools."""
    
    def test_date_extraction(self):
        """Test date extraction tool."""
        from tools.extraction_tools import extract_dates_tool
        
        test_text = "The expedition is on 2024-07-12, which is a Friday."
        result = extract_dates_tool(test_text)
        
        import json
        data = json.loads(result)
        
        self.assertIn("2024-07-12", data["dates"])
        self.assertIn("Friday", data["weekdays"])
    
    def test_location_extraction(self):
        """Test location extraction tool."""
        from tools.extraction_tools import extract_locations_tool
        
        test_text = "Location: The Lacepedes. We will arrive at Paradise Bay."
        result = extract_locations_tool(test_text)
        
        import json
        data = json.loads(result)
        
        self.assertGreater(len(data["locations"]), 0)
    
    def test_equipment_extraction(self):
        """Test equipment extraction tool."""
        from tools.extraction_tools import extract_equipment_tool
        
        test_text = "We need 8 zodiac boats and 1 twin boat for the operation."
        result = extract_equipment_tool(test_text)
        
        import json
        data = json.loads(result)
        
        self.assertEqual(data["zodiacs"], 8)
        self.assertEqual(data["twins"], 1)


if __name__ == "__main__":
    unittest.main()
