"""
Test individual components of Expo.
"""

import unittest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch

# Import Expo components
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from config.settings import ExpoSettings
from core.document_processor import DocumentProcessor
from core.json_generator import J<PERSON><PERSON>enerator
from core.output_manager import OutputManager
from agents.extraction_agent import ExtractionAgent


class TestExpoSettings(unittest.TestCase):
    """Test Expo settings configuration."""
    
    def test_default_settings(self):
        """Test default settings initialization."""
        settings = ExpoSettings()
        
        self.assertEqual(settings.app_name, "Expo")
        self.assertEqual(settings.version, "1.0.0")
        self.assertIn('.pdf', settings.supported_extensions)
        self.assertIn('.docx', settings.supported_extensions)
        self.assertTrue(settings.enable_ocr)
        self.assertTrue(settings.enable_validation)
    
    def test_file_support_check(self):
        """Test file support checking."""
        settings = ExpoSettings()
        
        self.assertTrue(settings.is_supported_file("test.pdf"))
        self.assertTrue(settings.is_supported_file("test.docx"))
        self.assertTrue(settings.is_supported_file("test.txt"))
        self.assertFalse(settings.is_supported_file("test.xyz"))


class TestDocumentProcessor(unittest.TestCase):
    """Test document processor functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.processor = DocumentProcessor()
    
    def test_initialization(self):
        """Test processor initialization."""
        self.assertIsNotNone(self.processor)
    
    def test_text_file_extraction(self):
        """Test text file extraction."""
        # Create temporary text file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("Test expedition document\nLocation: Test Site\nDate: 2024-07-12")
            temp_path = f.name
        
        try:
            # Extract text
            result = self.processor.extract_text(temp_path)
            
            self.assertIsNotNone(result)
            self.assertIn("Test expedition document", result)
            self.assertIn("Location: Test Site", result)
            
        finally:
            Path(temp_path).unlink()
    
    def test_file_info(self):
        """Test file information extraction."""
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            f.write("test content")
            temp_path = f.name
        
        try:
            info = self.processor.get_file_info(temp_path)
            
            self.assertIn("name", info)
            self.assertIn("size_bytes", info)
            self.assertIn("extension", info)
            self.assertEqual(info["extension"], ".txt")
            self.assertTrue(info["supported"])
            
        finally:
            Path(temp_path).unlink()


class TestJSONGenerator(unittest.TestCase):
    """Test JSON template generator."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.generator = JSONGenerator()
    
    def test_template_creation(self):
        """Test JSON template creation."""
        test_data = {
            "location": "Test Location",
            "date": "2024-07-12",
            "weekday": "Friday",
            "day_number": 2,
            "zodiacs": 8,
            "twins": 1,
            "groups": [
                {
                    "groupName": "Yellow",
                    "color": "Yellow",
                    "departureTime": "07:40",
                    "returnTime": "09:40"
                }
            ]
        }
        
        template = self.generator.create_template(test_data)
        
        self.assertEqual(template["location"], "Test Location")
        self.assertEqual(template["date"], "2024-07-12")
        self.assertEqual(template["weekday"], "Friday")
        self.assertEqual(template["dayNumber"], 2)
        self.assertEqual(template["zodiacs"], 8)
        self.assertEqual(template["twins"], 1)
        self.assertEqual(len(template["groups"]), 1)
    
    def test_template_validation(self):
        """Test template validation."""
        # Valid template
        valid_template = {
            "dayNumber": 1,
            "weekday": "Monday",
            "date": "2024-07-12",
            "location": "Test Location",
            "zodiacs": 8,
            "twins": 1,
            "groups": [],
            "schedule": [],
            "tides": []
        }
        
        errors = self.generator.validate_template(valid_template)
        self.assertEqual(len(errors), 0)
        
        # Invalid template
        invalid_template = {
            "dayNumber": "not_a_number",
            "date": "invalid_date",
            "groups": "not_a_list"
        }
        
        errors = self.generator.validate_template(invalid_template)
        self.assertGreater(len(errors), 0)


class TestOutputManager(unittest.TestCase):
    """Test output manager functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.manager = OutputManager(self.temp_dir)
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_initialization(self):
        """Test output manager initialization."""
        self.assertTrue(Path(self.temp_dir).exists())
        self.assertEqual(str(self.manager.output_directory), self.temp_dir)
    
    def test_json_saving(self):
        """Test JSON file saving."""
        test_data = {
            "location": "Test Location",
            "date": "2024-07-12",
            "dayNumber": 1,
            "weekday": "Friday"
        }
        
        output_path = self.manager.save_json(test_data)
        
        self.assertTrue(Path(output_path).exists())
        
        # Verify content
        with open(output_path, 'r') as f:
            saved_data = json.load(f)
        
        self.assertEqual(saved_data["location"], "Test Location")
        self.assertEqual(saved_data["date"], "2024-07-12")
    
    def test_filename_generation(self):
        """Test filename generation."""
        test_data = {
            "location": "The Lacepedes",
            "date": "2024-07-12"
        }
        
        filename = self.manager._generate_filename(test_data)
        self.assertEqual(filename, "the-lacepedes-2024-07-12.json")
    
    def test_session_stats(self):
        """Test session statistics."""
        # Save some test files
        for i in range(3):
            test_data = {
                "location": f"Location {i}",
                "date": f"2024-07-{12+i:02d}"
            }
            self.manager.save_json(test_data)
        
        stats = self.manager.get_session_stats()
        
        self.assertEqual(stats["total_files"], 3)
        self.assertEqual(len(stats["locations"]), 3)
        self.assertEqual(len(stats["dates"]), 3)


class TestExtractionAgent(unittest.TestCase):
    """Test extraction agent functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.agent = ExtractionAgent()
    
    def test_initialization(self):
        """Test agent initialization."""
        self.assertIsNotNone(self.agent)
    
    def test_fallback_extraction(self):
        """Test fallback regex extraction."""
        test_text = """
        Expedition Document
        Location: The Lacepedes
        Date: 2024-07-12
        8 Zodiac + 1 Twin
        Yellow Group: 07:40
        Blue Group: 09:40
        Low Tide: 02:00 - 2.0m
        """
        
        result = self.agent._fallback_extraction(test_text)
        
        self.assertIn("location", result)
        self.assertIn("zodiacs", result)
        self.assertIn("twins", result)
        self.assertEqual(result["zodiacs"], 8)
        self.assertEqual(result["twins"], 1)


if __name__ == "__main__":
    unittest.main()
