"""
LangChain tools for entity extraction.
"""

import re
import json
import logging
from typing import List, Dict, Any
from datetime import datetime

try:
    from langchain.tools import Tool
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False

logger = logging.getLogger(__name__)


def extract_dates_tool(text: str) -> str:
    """Extract dates from text."""
    try:
        dates = []
        
        # Date patterns
        patterns = [
            r'(\d{4}-\d{2}-\d{2})',  # YYYY-MM-DD
            r'(\d{1,2}/\d{1,2}/\d{4})',  # MM/DD/YYYY or DD/MM/YYYY
            r'(\d{1,2}-\d{1,2}-\d{4})',  # MM-DD-YYYY or DD-MM-YYYY
            r'(\w+\s+\d{1,2},?\s+\d{4})',  # Month DD, YYYY
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            dates.extend(matches)
        
        # Also extract weekdays
        weekdays = re.findall(r'\b(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)\b', text, re.IGNORECASE)
        
        result = {
            "dates": list(set(dates)),
            "weekdays": list(set(weekdays))
        }
        
        return json.dumps(result)
        
    except Exception as e:
        return json.dumps({"error": str(e), "dates": [], "weekdays": []})


def extract_locations_tool(text: str) -> str:
    """Extract location information from text."""
    try:
        locations = []
        
        # Location patterns
        patterns = [
            r'Location[:\s]+([A-Za-z\s,]+)',
            r'Site[:\s]+([A-Za-z\s,]+)',
            r'Destination[:\s]+([A-Za-z\s,]+)',
            r'at\s+([A-Z][A-Za-z\s]+)',
            r'in\s+([A-Z][A-Za-z\s]+)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                clean_location = match.strip().strip(',')
                if len(clean_location) > 2 and len(clean_location) < 50:
                    locations.append(clean_location)
        
        # Remove duplicates and common words
        common_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        locations = [loc for loc in set(locations) if loc.lower() not in common_words]
        
        return json.dumps({"locations": locations})
        
    except Exception as e:
        return json.dumps({"error": str(e), "locations": []})


def extract_times_tool(text: str) -> str:
    """Extract time information from text."""
    try:
        times = []
        
        # Time patterns
        patterns = [
            r'(\d{1,2}:\d{2}(?:\s*[AaPp][Mm])?)',  # HH:MM or HH:MM AM/PM
            r'(\d{1,2}\.\d{2})',  # HH.MM format
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            times.extend(matches)
        
        # Clean and deduplicate times
        clean_times = []
        for time_str in set(times):
            # Convert to standard format
            time_str = time_str.strip()
            if re.match(r'\d{1,2}:\d{2}', time_str):
                clean_times.append(time_str)
        
        return json.dumps({"times": clean_times})
        
    except Exception as e:
        return json.dumps({"error": str(e), "times": []})


def extract_groups_tool(text: str) -> str:
    """Extract group information from text."""
    try:
        groups = []
        
        # Color group patterns
        colors = ['red', 'blue', 'yellow', 'green', 'orange', 'purple', 'pink', 'white', 'black']
        
        for color in colors:
            # Look for color group mentions
            pattern = rf'\b{color}\s*group\b'
            if re.search(pattern, text, re.IGNORECASE):
                groups.append({
                    "groupName": color.title(),
                    "color": color.title(),
                    "activity": "Unknown"
                })
        
        # Look for group with times
        group_time_pattern = r'(\w+)\s*group[:\s]*(\d{1,2}:\d{2})'
        matches = re.findall(group_time_pattern, text, re.IGNORECASE)
        
        for group_name, time in matches:
            groups.append({
                "groupName": group_name.title(),
                "color": group_name.title(),
                "departureTime": time,
                "activity": "Unknown"
            })
        
        return json.dumps({"groups": groups})
        
    except Exception as e:
        return json.dumps({"error": str(e), "groups": []})


def extract_equipment_tool(text: str) -> str:
    """Extract equipment and logistics information."""
    try:
        equipment = {
            "zodiacs": 0,
            "twins": 0,
            "notes": ""
        }
        
        # Extract zodiac count
        zodiac_pattern = r'(\d+)\s*zodiac'
        zodiac_match = re.search(zodiac_pattern, text, re.IGNORECASE)
        if zodiac_match:
            equipment["zodiacs"] = int(zodiac_match.group(1))
        
        # Extract twin count
        twin_pattern = r'(\d+)\s*twin'
        twin_match = re.search(twin_pattern, text, re.IGNORECASE)
        if twin_match:
            equipment["twins"] = int(twin_match.group(1))
        
        # Extract equipment notes
        equipment_keywords = ['equipment', 'gear', 'setup', 'briefing', 'safety']
        notes = []
        
        for keyword in equipment_keywords:
            pattern = rf'{keyword}[:\s]*([^.!?]*[.!?])'
            matches = re.findall(pattern, text, re.IGNORECASE)
            notes.extend(matches)
        
        if notes:
            equipment["notes"] = " ".join(notes[:3])  # Limit to first 3 notes
        
        return json.dumps(equipment)
        
    except Exception as e:
        return json.dumps({"error": str(e), "zodiacs": 0, "twins": 0, "notes": ""})


def extract_tides_tool(text: str) -> str:
    """Extract tide information from text."""
    try:
        tides = []
        
        # Tide patterns
        tide_patterns = [
            r'(\d{1,2}:\d{2})\s*[:\-\s]*(\d+\.?\d*)\s*[m]?\s*(low|high)\s*tide',
            r'(low|high)\s*tide[:\s]*(\d{1,2}:\d{2})\s*[:\-\s]*(\d+\.?\d*)',
        ]
        
        for pattern in tide_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match) == 3:
                    if match[0].count(':') == 1:  # First element is time
                        time, height, tide_type = match
                    else:  # First element is tide type
                        tide_type, time, height = match
                    
                    try:
                        height_float = float(height)
                        tides.append({
                            "time": time,
                            "height": height_float,
                            "label": f"{tide_type.title()} Tide"
                        })
                    except ValueError:
                        continue
        
        return json.dumps({"tides": tides})
        
    except Exception as e:
        return json.dumps({"error": str(e), "tides": []})


def create_extraction_tools() -> List[Tool]:
    """Create list of extraction tools for LangChain agent."""
    if not LANGCHAIN_AVAILABLE:
        return []

    tools = [
        Tool(
            name="extract_dates",
            description="Extract dates and weekdays from expedition text. Takes text as input and returns JSON with dates and weekdays.",
            func=extract_dates_tool
        ),
        Tool(
            name="extract_locations",
            description="Extract location names and destinations from expedition text. Takes text as input and returns JSON with locations.",
            func=extract_locations_tool
        ),
        Tool(
            name="extract_times",
            description="Extract time information (schedules, arrivals, departures) from expedition text. Takes text as input and returns JSON with times.",
            func=extract_times_tool
        ),
        Tool(
            name="extract_groups",
            description="Extract group information (colors, names, activities) from expedition text. Takes text as input and returns JSON with groups.",
            func=extract_groups_tool
        ),
        Tool(
            name="extract_equipment",
            description="Extract equipment and logistics information (zodiacs, twins, notes) from expedition text. Takes text as input and returns JSON with equipment.",
            func=extract_equipment_tool
        ),
        Tool(
            name="extract_tides",
            description="Extract tide information (times, heights, types) from expedition text. Takes text as input and returns JSON with tides.",
            func=extract_tides_tool
        ),
    ]

    return tools
