"""
Lang<PERSON>hain tools for entity extraction.
"""

import re
import json
import logging
from typing import List, Dict, Any
from datetime import datetime

try:
    from langchain.tools import Tool
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False

logger = logging.getLogger(__name__)


def extract_dates_tool(text: str) -> str:
    """Extract and normalize dates from text."""
    try:
        dates = []
        weekdays = []

        # Month name mappings for normalization
        month_map = {
            'january': '01', 'jan': '01', 'february': '02', 'feb': '02',
            'march': '03', 'mar': '03', 'april': '04', 'apr': '04',
            'may': '05', 'june': '06', 'jun': '06', 'july': '07', 'jul': '07',
            'august': '08', 'aug': '08', 'september': '09', 'sep': '09',
            'october': '10', 'oct': '10', 'november': '11', 'nov': '11',
            'december': '12', 'dec': '12'
        }

        # Enhanced date patterns including ordinal dates
        patterns = [
            # Standard formats
            (r'(\d{4}-\d{2}-\d{2})', 'standard'),  # YYYY-MM-DD
            (r'(\d{1,2}/\d{1,2}/\d{4})', 'standard'),  # MM/DD/YYYY
            (r'(\d{1,2}-\d{1,2}-\d{4})', 'standard'),  # MM-DD-YYYY
            # Ordinal dates like "17th March"
            (r'(\d{1,2})(st|nd|rd|th)?\s+(January|February|March|April|May|June|July|August|September|October|November|December)', 'ordinal'),
            (r'(\d{1,2})(st|nd|rd|th)?\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)', 'ordinal'),
            # Month day formats
            (r'(January|February|March|April|May|June|July|August|September|October|November|December)\s+(\d{1,2})(st|nd|rd|th)?', 'month_first'),
            (r'(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+(\d{1,2})(st|nd|rd|th)?', 'month_first'),
        ]

        for pattern, pattern_type in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if pattern_type == 'standard':
                    dates.append(match)
                elif pattern_type == 'ordinal':
                    # Handle "17th March" format
                    day = match[0].zfill(2)
                    month_name = match[2].lower()
                    month = month_map.get(month_name, '01')
                    current_year = datetime.now().year
                    normalized_date = f"{current_year}-{month}-{day}"
                    dates.append(normalized_date)
                elif pattern_type == 'month_first':
                    # Handle "March 17th" format
                    month_name = match[0].lower()
                    day = match[1].zfill(2)
                    month = month_map.get(month_name, '01')
                    current_year = datetime.now().year
                    normalized_date = f"{current_year}-{month}-{day}"
                    dates.append(normalized_date)

        # Extract weekdays
        weekday_matches = re.findall(r'\b(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)\b', text, re.IGNORECASE)
        weekdays.extend([day.title() for day in weekday_matches])

        result = {
            "dates": list(set(dates)),
            "weekdays": list(set(weekdays))
        }

        return json.dumps(result)

    except Exception as e:
        return json.dumps({"error": str(e), "dates": [], "weekdays": []})


def extract_locations_tool(text: str) -> str:
    """Extract location candidates using document structure and lexical patterns."""
    try:
        locations = []

        # Enhanced location patterns with document structure awareness
        patterns = [
            # Explicit location markers
            (r'Location[:\s]+([A-Za-z\s,.-–—]+?)(?:\n|$|Date|Day|Time)', 'explicit'),
            (r'Site[:\s]+([A-Za-z\s,.-–—]+?)(?:\n|$|Date|Day|Time)', 'explicit'),
            (r'Destination[:\s]+([A-Za-z\s,.-–—]+?)(?:\n|$|Date|Day|Time)', 'explicit'),
            (r'Island[:\s]+([A-Za-z\s,.-–—]+?)(?:\n|$|Date|Day|Time)', 'explicit'),
            (r'Port[:\s]+([A-Za-z\s,.-–—]+?)(?:\n|$|Date|Day|Time)', 'explicit'),

            # Japanese location patterns (common in expedition documents)
            (r'([A-Za-z]+jima)\b', 'japanese'),  # -jima (island)
            (r'([A-Za-z]+shima)\b', 'japanese'),  # -shima (island)
            (r'([A-Za-z]+mura)\b', 'japanese'),  # -mura (village)
            (r'([A-Za-z]+cho)\b', 'japanese'),   # -cho (town)

            # Geographic patterns with connectors (dashes, em-dashes)
            (r'([A-Z][a-z]+(?:\s*[–—-]\s*[A-Z][a-z]+)+)', 'geographic'),

            # Document structure patterns (first line, headers)
            (r'^([A-Z][A-Za-z\s,.-–—]+?)(?:\s+\d|\n|$)', 'header'),

            # Contextual location patterns
            (r'at\s+([A-Z][A-Za-z\s,.-–—]+?)(?:\n|$|Date|Day|Time|\d)', 'contextual'),
            (r'in\s+([A-Z][A-Za-z\s,.-–—]+?)(?:\n|$|Date|Day|Time|\d)', 'contextual'),
            (r'to\s+([A-Z][A-Za-z\s,.-–—]+?)(?:\n|$|Date|Day|Time|\d)', 'contextual'),

            # Marine/expedition specific patterns
            (r'([A-Z][a-z]+\s+(?:Bay|Harbor|Harbour|Marina|Port|Island|Beach|Cove))', 'marine'),
            (r'((?:Bay|Harbor|Harbour|Marina|Port|Island|Beach|Cove)\s+[A-Z][a-z]+)', 'marine'),
        ]

        location_candidates = {}  # Use dict to track confidence scores

        for pattern, pattern_type in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                if isinstance(match, tuple):
                    location = match[0] if match[0] else match[1] if len(match) > 1 else ""
                else:
                    location = match

                location = location.strip().strip(',.-')

                # Validate location candidate
                if _is_valid_location_candidate(location):
                    # Assign confidence based on pattern type
                    confidence = _get_location_confidence(pattern_type, location)

                    if location not in location_candidates or location_candidates[location] < confidence:
                        location_candidates[location] = confidence

        # Sort by confidence and return top candidates
        sorted_locations = sorted(location_candidates.items(), key=lambda x: x[1], reverse=True)
        locations = [loc for loc, conf in sorted_locations if conf > 0.3]  # Minimum confidence threshold

        return json.dumps({"locations": locations[:5]})  # Return top 5 candidates

    except Exception as e:
        return json.dumps({"error": str(e), "locations": []})

def _is_valid_location_candidate(location: str) -> bool:
    """Validate if text looks like a location."""
    if not location or len(location) < 2 or len(location) > 100:
        return False

    # Exclude common non-location words
    exclude_patterns = [
        r'\b(crew|team|staff|guide|driver|captain|boat|zodiac|equipment)\b',
        r'\b(morning|afternoon|evening|night|today|tomorrow|yesterday)\b',
        r'\b(red|blue|green|yellow|orange|purple|pink|white|black)\s*group\b',
        r'^\d+$',  # Just numbers
        r'^[a-z]+$',  # All lowercase (likely not a proper noun)
    ]

    for pattern in exclude_patterns:
        if re.search(pattern, location, re.IGNORECASE):
            return False

    # Check for crew name patterns (multiple short names)
    names = re.split(r'[,\s]+', location.strip())
    if len(names) >= 2:
        short_names = [name for name in names if 2 <= len(name) <= 10 and name.isalpha()]
        if len(short_names) >= 2:
            return False  # Likely crew names

    return True

def _get_location_confidence(pattern_type: str, location: str) -> float:
    """Calculate confidence score for location candidate."""
    base_confidence = {
        'explicit': 0.9,      # "Location: Iheyajima"
        'japanese': 0.8,      # "Iheyajima" (ends with -jima)
        'geographic': 0.7,    # "Iheyajima – Maedomari"
        'marine': 0.7,        # "Marina Bay"
        'header': 0.6,        # First line of document
        'contextual': 0.5,    # "at Iheyajima"
    }

    confidence = base_confidence.get(pattern_type, 0.3)

    # Boost confidence for certain indicators
    if re.search(r'(jima|shima|island|bay|port|marina)$', location, re.IGNORECASE):
        confidence += 0.1

    if re.search(r'[–—-]', location):  # Contains dashes (geographic names)
        confidence += 0.1

    if location[0].isupper():  # Starts with capital (proper noun)
        confidence += 0.05

    return min(confidence, 1.0)  # Cap at 1.0


def extract_times_tool(text: str) -> str:
    """Extract time information from text."""
    try:
        times = []
        
        # Time patterns
        patterns = [
            r'(\d{1,2}:\d{2}(?:\s*[AaPp][Mm])?)',  # HH:MM or HH:MM AM/PM
            r'(\d{1,2}\.\d{2})',  # HH.MM format
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            times.extend(matches)
        
        # Clean and deduplicate times
        clean_times = []
        for time_str in set(times):
            # Convert to standard format
            time_str = time_str.strip()
            if re.match(r'\d{1,2}:\d{2}', time_str):
                clean_times.append(time_str)
        
        return json.dumps({"times": clean_times})
        
    except Exception as e:
        return json.dumps({"error": str(e), "times": []})


def extract_groups_tool(text: str) -> str:
    """Extract group information with enhanced patterns."""
    try:
        groups = []
        seen_groups = set()  # Avoid duplicates

        # Enhanced color group patterns
        colors = ['red', 'blue', 'yellow', 'green', 'orange', 'purple', 'pink', 'white', 'black']

        # Pattern 1: "Disembarkation Red group" or "Red Group" with times
        for color in colors:
            patterns = [
                rf'(\d{{1,2}}:\d{{2}})\s+.*?{color}\s*group',  # Time before group
                rf'{color}\s*group[:\s]*(\d{{1,2}}:\d{{2}})',  # Group before time
                rf'Disembarkation\s+{color}\s*group[:\s]*(\d{{1,2}}:\d{{2}})?',  # Disembarkation pattern
                rf'(\d{{1,2}}:\d{{2}})\s+Disembarkation\s+{color}\s*group',  # Time + Disembarkation + Group
            ]

            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                for match in matches:
                    time = match if isinstance(match, str) else (match[0] if match[0] else "")
                    group_key = f"{color.title()}-{time}"

                    if group_key not in seen_groups:
                        seen_groups.add(group_key)
                        group_data = {
                            "groupName": color.title(),
                            "color": color.title(),
                            "activity": "Disembarkation"
                        }
                        if time:
                            group_data["departureTime"] = time
                        groups.append(group_data)

        # Pattern 2: "Group X to Y" patterns for activities
        activity_patterns = [
            r'(\w+)\s*group\s+to\s+(\w+)',  # "Red group to beach"
            r'(\w+)\s*group\s+(\w+\s+\w+)',  # "Red group zodiac cruise"
            r'(\w+)\s*group[:\s]+(\d{1,2}:\d{2})\s*-\s*(\d{1,2}:\d{2})',  # "Red group: 09:00 - 11:00"
        ]

        for pattern in activity_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match) >= 2:
                    group_name = match[0].title()
                    activity = match[1] if len(match) == 2 else f"{match[1]} - {match[2]}"

                    group_key = f"{group_name}-{activity}"
                    if group_key not in seen_groups:
                        seen_groups.add(group_key)
                        groups.append({
                            "groupName": group_name,
                            "color": group_name,
                            "activity": activity.title(),
                            "startTime": match[1] if len(match) == 3 and ":" in match[1] else "",
                            "endTime": match[2] if len(match) == 3 and ":" in match[2] else ""
                        })

        # Pattern 3: General group mentions with context
        general_patterns = [
            r'(\w+)\s*group[:\s]*(\d{1,2}:\d{2})',  # Any group with time
            r'(\d{1,2}:\d{2})\s*(\w+)\s*group',  # Time then group
        ]

        for pattern in general_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match) == 2:
                    if ":" in match[0]:  # Time first
                        time, group_name = match
                    else:  # Group first
                        group_name, time = match

                    group_key = f"{group_name.title()}-{time}"
                    if group_key not in seen_groups and group_name.lower() in [c.lower() for c in colors]:
                        seen_groups.add(group_key)
                        groups.append({
                            "groupName": group_name.title(),
                            "color": group_name.title(),
                            "departureTime": time,
                            "activity": "Unknown"
                        })

        return json.dumps({"groups": groups})

    except Exception as e:
        return json.dumps({"error": str(e), "groups": []})


def extract_activities_tool(text: str) -> str:
    """Extract activity information with Group X to Y patterns."""
    try:
        activities = []

        # Pattern 1: "Group X to Y" patterns
        group_to_patterns = [
            r'(\w+)\s*group\s+to\s+(\w+)',  # "Red group to beach"
            r'(\w+)\s*group\s+to\s+(\w+\s+\w+)',  # "Red group to zodiac cruise"
            r'(\w+)\s*group\s+(\w+\s+cruise)',  # "Red group zodiac cruise"
            r'(\w+)\s*group\s+(\w+\s+landing)',  # "Red group beach landing"
        ]

        for pattern in group_to_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                group_name = match[0].title()
                activity_type = match[1].title()

                activities.append({
                    "groupName": group_name,
                    "activityType": activity_type,
                    "startTime": "",
                    "endTime": "",
                    "location": ""
                })

        # Pattern 2: Time-based activity patterns
        time_activity_patterns = [
            r'(\d{1,2}:\d{2})\s+(\w+)\s*group\s+(\w+)',  # "09:00 Red group disembark"
            r'(\w+)\s*group[:\s]*(\d{1,2}:\d{2})\s*-\s*(\d{1,2}:\d{2})\s*(\w+)',  # "Red group: 09:00 - 11:00 cruise"
            r'(\d{1,2}:\d{2})\s+Disembarkation\s+(\w+)\s*group',  # "09:00 Disembarkation Red group"
        ]

        for pattern in time_activity_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match) == 3:  # Time, group, activity
                    start_time = match[0] if ":" in match[0] else ""
                    group_name = match[1].title()
                    activity_type = match[2].title()
                    end_time = ""
                elif len(match) == 4:  # Group, start, end, activity
                    group_name = match[0].title()
                    start_time = match[1]
                    end_time = match[2]
                    activity_type = match[3].title()
                else:
                    continue

                activities.append({
                    "groupName": group_name,
                    "activityType": activity_type,
                    "startTime": start_time,
                    "endTime": end_time,
                    "location": ""
                })

        # Pattern 3: Activity duration patterns
        duration_patterns = [
            r'(\w+)\s*group[:\s]*(\d+h?)\s*(\w+)',  # "Red group 2h cruise"
            r'(\w+)\s*group[:\s]*(\d{1,2}:\d{2})\s*(\w+\s+\w+)',  # "Red group 09:00 zodiac cruise"
        ]

        for pattern in duration_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                group_name = match[0].title()
                time_or_duration = match[1]
                activity_type = match[2].title()

                activity_data = {
                    "groupName": group_name,
                    "activityType": activity_type,
                    "location": ""
                }

                if ":" in time_or_duration:
                    activity_data["startTime"] = time_or_duration
                    activity_data["endTime"] = ""
                else:
                    activity_data["duration"] = time_or_duration
                    activity_data["startTime"] = ""
                    activity_data["endTime"] = ""

                activities.append(activity_data)

        return json.dumps({"activities": activities})

    except Exception as e:
        return json.dumps({"error": str(e), "activities": []})


def extract_equipment_tool(text: str) -> str:
    """Extract equipment and logistics information."""
    try:
        equipment = {
            "zodiacs": 0,
            "twins": 0,
            "notes": ""
        }
        
        # Extract zodiac count
        zodiac_pattern = r'(\d+)\s*zodiac'
        zodiac_match = re.search(zodiac_pattern, text, re.IGNORECASE)
        if zodiac_match:
            equipment["zodiacs"] = int(zodiac_match.group(1))
        
        # Extract twin count
        twin_pattern = r'(\d+)\s*twin'
        twin_match = re.search(twin_pattern, text, re.IGNORECASE)
        if twin_match:
            equipment["twins"] = int(twin_match.group(1))
        
        # Extract equipment notes
        equipment_keywords = ['equipment', 'gear', 'setup', 'briefing', 'safety']
        notes = []
        
        for keyword in equipment_keywords:
            pattern = rf'{keyword}[:\s]*([^.!?]*[.!?])'
            matches = re.findall(pattern, text, re.IGNORECASE)
            notes.extend(matches)
        
        if notes:
            equipment["notes"] = " ".join(notes[:3])  # Limit to first 3 notes
        
        return json.dumps(equipment)
        
    except Exception as e:
        return json.dumps({"error": str(e), "zodiacs": 0, "twins": 0, "notes": ""})


def extract_tides_tool(text: str) -> str:
    """Extract tide information from text."""
    try:
        tides = []
        
        # Tide patterns
        tide_patterns = [
            r'(\d{1,2}:\d{2})\s*[:\-\s]*(\d+\.?\d*)\s*[m]?\s*(low|high)\s*tide',
            r'(low|high)\s*tide[:\s]*(\d{1,2}:\d{2})\s*[:\-\s]*(\d+\.?\d*)',
        ]
        
        for pattern in tide_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match) == 3:
                    if match[0].count(':') == 1:  # First element is time
                        time, height, tide_type = match
                    else:  # First element is tide type
                        tide_type, time, height = match
                    
                    try:
                        height_float = float(height)
                        tides.append({
                            "time": time,
                            "height": height_float,
                            "label": f"{tide_type.title()} Tide"
                        })
                    except ValueError:
                        continue
        
        return json.dumps({"tides": tides})
        
    except Exception as e:
        return json.dumps({"error": str(e), "tides": []})


def create_extraction_tools() -> List[Tool]:
    """Create list of extraction tools for LangChain agent."""
    if not LANGCHAIN_AVAILABLE:
        return []

    tools = [
        Tool(
            name="extract_dates",
            description="Extract dates and weekdays from expedition text. Takes text as input and returns JSON with dates and weekdays.",
            func=extract_dates_tool
        ),
        Tool(
            name="extract_locations",
            description="Extract location names and destinations from expedition text. Takes text as input and returns JSON with locations.",
            func=extract_locations_tool
        ),
        Tool(
            name="extract_times",
            description="Extract time information (schedules, arrivals, departures) from expedition text. Takes text as input and returns JSON with times.",
            func=extract_times_tool
        ),
        Tool(
            name="extract_groups",
            description="Extract group information (colors, names, activities) from expedition text. Takes text as input and returns JSON with groups.",
            func=extract_groups_tool
        ),
        Tool(
            name="extract_activities",
            description="Extract activity information with Group X to Y patterns from expedition text. Takes text as input and returns JSON with activities.",
            func=extract_activities_tool
        ),
        Tool(
            name="extract_equipment",
            description="Extract equipment and logistics information (zodiacs, twins, notes) from expedition text. Takes text as input and returns JSON with equipment.",
            func=extract_equipment_tool
        ),
        Tool(
            name="extract_tides",
            description="Extract tide information (times, heights, types) from expedition text. Takes text as input and returns JSON with tides.",
            func=extract_tides_tool
        ),
    ]

    return tools
