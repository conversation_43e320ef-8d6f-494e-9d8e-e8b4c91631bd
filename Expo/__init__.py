"""
Expo - Modular Expedition Planner

A clean, standalone rebuild of the expedition planner with focus on:
- Document ingestion (PDF, DOCX, DOC, TXT)
- Docling-based text extraction
- LangChain agent-based processing
- JSON template generation
- Minimal UI interface

This version is completely independent from the original expedition_planner.
"""

__version__ = "1.0.0"
__author__ = "Expedition Planner Team"

# Core modules
from .core.document_processor import DocumentProcessor
from .core.json_generator import JSONGenerator

# Agent modules
from .agents.extraction_agent import ExtractionAgent

# UI modules
from .ui.file_picker import FilePicker

__all__ = [
    "DocumentProcessor",
    "JSONGenerator", 
    "ExtractionAgent",
    "FilePicker",
]
