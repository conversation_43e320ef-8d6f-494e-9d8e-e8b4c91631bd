# Getting Started with Expo

## 🚀 Quick Start

### 1. Installation

```bash
cd Expo
chmod +x install.sh
./install.sh
```

### 2. Activate Environment

```bash
source expo_env/bin/activate
```

### 3. Run Demo

```bash
python demo.py
```

### 4. Use Expo

**GUI Interface:**
```bash
python -m Expo.main
# or
expo-gui
```

**CLI Interface:**
```bash
python -m Expo.cli document.pdf
# or  
expo document.pdf
```

## 📁 Project Structure

```
Expo/
├── 📄 README.md              # Project overview
├── 🔧 main.py                # Main GUI application
├── 💻 cli.py                 # Command-line interface
├── 🎯 demo.py                # Demo with sample documents
├── 🧪 run_tests.py           # Test runner
├── ⚙️ install.sh             # Installation script
├── 📦 requirements.txt       # Dependencies
├── 🔨 setup.py               # Package setup
│
├── config/                   # Configuration
│   ├── settings.py           # App settings
│   └── ollama_config.py      # LLM configuration
│
├── core/                     # Core processing
│   ├── document_processor.py # Document ingestion
│   ├── json_generator.py     # JSON template creation
│   └── output_manager.py     # Output handling
│
├── agents/                   # LangChain agents
│   └── extraction_agent.py   # Entity extraction
│
├── tools/                    # LangChain tools
│   └── extraction_tools.py   # Extraction utilities
│
├── ui/                       # User interface
│   └── file_picker.py        # File selection GUI
│
├── tests/                    # Test suite
│   ├── test_components.py    # Unit tests
│   └── test_integration.py   # Integration tests
│
└── output/                   # Generated JSON files
```

## 🎯 Features

### ✅ Phase 1 - Complete
- **Document Ingestion**: PDF, DOCX, DOC, TXT support
- **Docling Integration**: Advanced text extraction
- **LangChain Agents**: Entity extraction with Ollama
- **JSON Generation**: Structured expedition templates
- **GUI Interface**: Simple file picker with progress
- **CLI Interface**: Command-line processing
- **Output Management**: Automatic saving with naming
- **Fallback Processing**: Works without LangChain
- **Comprehensive Tests**: Unit and integration tests

### 🔄 Processing Pipeline

1. **File Selection** → Select expedition documents
2. **Text Extraction** → Docling processes documents  
3. **Entity Extraction** → LangChain agent extracts data
4. **JSON Generation** → Creates structured templates
5. **Output Saving** → Saves to `~/Desktop/output/`

### 📊 JSON Template Structure

```json
{
  "dayNumber": 2,
  "weekday": "Friday",
  "date": "2024-07-12", 
  "location": "The Lacepedes",
  "utcOffset": "+00:00",
  "notes": "Equipment and setup notes",
  "zodiacs": 8,
  "twins": 1,
  "activityType": "2h Zodiac Cruise",
  "groups": [...],
  "schedule": [...],
  "tides": [...],
  "groupOrder": ["Yellow", "Blue", "Red", "Green"],
  "zodiacDrivers": [...],
  "landingGuides": {...},
  "nextDayLocation": "Next Location",
  "sunTimes": {...}
}
```

## 🛠️ Configuration

### Environment Variables (.env)
```bash
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=mistral-7b-v0-1-gguf:latest
OUTPUT_DIRECTORY=~/Desktop/output
ENABLE_OCR=true
```

### Settings (config/settings.py)
- Supported file types
- Output directory
- Processing options
- Validation settings

## 🧪 Testing

### Run All Tests
```bash
python run_tests.py
```

### Run Specific Tests
```bash
python -m unittest tests.test_components
python -m unittest tests.test_integration
```

## 🔧 Dependencies

### Required
- **docling**: Document processing
- **langchain**: Agent framework  
- **ollama**: Local LLM integration
- **tkinter**: GUI (built-in)

### Optional
- **pytesseract**: OCR support
- **pillow**: Image processing

## 📝 Usage Examples

### Process Single File
```python
from Expo import ExpoApp

app = ExpoApp()
result = app.process_single_file("expedition.pdf")
print(f"Generated: {result}")
```

### Process Multiple Files
```python
files = ["doc1.pdf", "doc2.docx", "doc3.txt"]
results = app.process_files(files)
print(f"Generated {len(results)} JSON files")
```

### Custom Output Directory
```bash
expo document.pdf -o ~/custom_output/
```

## 🚨 Troubleshooting

### Docling Issues
```bash
pip install --upgrade docling
```

### Ollama Connection
```bash
ollama serve
ollama pull mistral-7b-v0-1-gguf:latest
```

### GUI Issues
```bash
# Install tkinter (if missing)
sudo apt-get install python3-tk  # Ubuntu
brew install python-tk           # macOS
```

## 🎉 Success!

You now have a fully functional, modular expedition planner that:
- ✅ Runs independently from the original app
- ✅ Processes multiple document formats
- ✅ Uses LangChain agents for intelligent extraction
- ✅ Generates structured JSON templates
- ✅ Has both GUI and CLI interfaces
- ✅ Includes comprehensive tests
- ✅ Handles errors gracefully

**Next Steps:**
1. Process your own expedition documents
2. Customize the JSON template structure
3. Add new extraction patterns
4. Integrate with other tools
