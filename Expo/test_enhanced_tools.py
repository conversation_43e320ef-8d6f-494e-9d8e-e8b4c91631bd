#!/usr/bin/env python3
"""
Test the enhanced extraction tools.
"""

import sys
import json
from tools.extraction_tools import (
    extract_dates_tool,
    extract_locations_tool,
    extract_groups_tool,
    extract_activities_tool
)

def test_enhanced_tools():
    """Test the enhanced extraction tools."""
    
    # Test document text from Iheyajima
    test_text = """
    Iheyajima – Maedomari- Yonezaki 17th March
    4 zodiacs (mach5) - 1 ladder – 1 <PERSON> craddle
    8:00 Schedule arrival time
    8:00 Scout boat <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>
    8:30 Drop<PERSON>, <PERSON>, <PERSON>
    8:30 ALL Team disembark – stand by at marina
    8:50 Life jacket : <PERSON><PERSON><PERSON><PERSON>
    9:00 Disembarkation Red group
    9:15 Disembarkation Green group
    9:30 Disembarkation Blue Group
    9:45 start Crew
    Then Keep 2 zodiacs for shuttle, other driver can attached the zodiac but stay stand by on 71
    12:45 Last zodiac
    13:15 Schedule departure
    14:30 Schedule arrival time
    14:30 Dropping zodiac <PERSON>, <PERSON>, <PERSON>, <PERSON> T
    14:30 ALL team stand by
    15:00 Disembarkation Red group
    15:15 Disembarkation Green group
    15:30 Disembarkation Blue Group
    French speaker on bus Kento
    15:45 Crew
    15:45 1 Zodiac with ladder in safety zodiac : <PERSON> (rotation whenever you want, just call)
    15:45 2 Zodiacs shuttles
    17:00 Last zodiac
    18:00 Briefing Zamami theatre (2 languages)
    19:00 Gala
    Tide High 11:09 1,5m Low 05:01 1,1m 19:09 0,5m
    """
    
    print("🧪 Testing Enhanced Extraction Tools")
    print("=" * 50)
    
    # Test 1: Enhanced Date Extraction
    print("\n📅 Testing Enhanced Date Extraction:")
    dates_result = extract_dates_tool(test_text)
    dates_data = json.loads(dates_result)
    print(f"Dates: {dates_data.get('dates', [])}")
    print(f"Weekdays: {dates_data.get('weekdays', [])}")
    
    # Test 2: Enhanced Location Extraction
    print("\n📍 Testing Enhanced Location Extraction:")
    locations_result = extract_locations_tool(test_text)
    locations_data = json.loads(locations_result)
    print(f"Locations: {locations_data.get('locations', [])}")
    
    # Test 3: Enhanced Group Extraction
    print("\n👥 Testing Enhanced Group Extraction:")
    groups_result = extract_groups_tool(test_text)
    groups_data = json.loads(groups_result)
    print(f"Groups found: {len(groups_data.get('groups', []))}")
    for i, group in enumerate(groups_data.get('groups', [])[:3]):  # Show first 3
        print(f"  Group {i+1}: {group}")
    
    # Test 4: New Activities Extraction
    print("\n🎯 Testing New Activities Extraction:")
    activities_result = extract_activities_tool(test_text)
    activities_data = json.loads(activities_result)
    print(f"Activities found: {len(activities_data.get('activities', []))}")
    for i, activity in enumerate(activities_data.get('activities', [])[:3]):  # Show first 3
        print(f"  Activity {i+1}: {activity}")
    
    print("\n" + "=" * 50)
    print("✅ Enhanced Tools Test Complete!")
    
    # Summary
    total_dates = len(dates_data.get('dates', []))
    total_locations = len(locations_data.get('locations', []))
    total_groups = len(groups_data.get('groups', []))
    total_activities = len(activities_data.get('activities', []))
    
    print(f"\n📊 Summary:")
    print(f"  📅 Dates extracted: {total_dates}")
    print(f"  📍 Locations extracted: {total_locations}")
    print(f"  👥 Groups extracted: {total_groups}")
    print(f"  🎯 Activities extracted: {total_activities}")
    
    # Check for key improvements
    improvements = []
    if any("2025-03-17" in date for date in dates_data.get('dates', [])):
        improvements.append("✅ Date normalization working (17th March → 2025-03-17)")
    
    if any("Iheyajima" in loc for loc in locations_data.get('locations', [])):
        improvements.append("✅ Location extraction working (found Iheyajima)")
    
    if total_groups >= 3:
        improvements.append("✅ Group extraction improved (found Red, Green, Blue)")
    
    if total_activities > 0:
        improvements.append("✅ Activity extraction working (new tool)")
    
    if improvements:
        print(f"\n🎉 Key Improvements:")
        for improvement in improvements:
            print(f"  {improvement}")
    
    return True

if __name__ == "__main__":
    try:
        success = test_enhanced_tools()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
