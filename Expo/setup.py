"""
Setup script for Expo - Expedition Document Processor
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding="utf-8") if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_path.exists():
    with open(requirements_path, 'r', encoding='utf-8') as f:
        requirements = [
            line.strip() 
            for line in f 
            if line.strip() and not line.startswith('#')
        ]

setup(
    name="expo-expedition-planner",
    version="1.0.0",
    description="Modular expedition document processor with LangChain agents",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Expedition Planner Team",
    author_email="<EMAIL>",
    url="https://github.com/GojiBarry/File",
    packages=find_packages(),
    include_package_data=True,
    install_requires=requirements,
    python_requires=">=3.9",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Office/Business :: Scheduling",
        "Topic :: Text Processing :: General",
    ],
    keywords=[
        "expedition",
        "document-processing", 
        "langchain",
        "ai-analysis",
        "json-generation",
        "ollama",
        "offline-llm",
        "docling",
    ],
    entry_points={
        "console_scripts": [
            "expo=Expo.cli:main",
            "expo-gui=Expo.main:main",
        ],
    },
    project_urls={
        "Bug Reports": "https://github.com/GojiBarry/File/issues",
        "Source": "https://github.com/GojiBarry/File",
    },
)
