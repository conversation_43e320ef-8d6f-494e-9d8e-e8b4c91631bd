"""
Command-line interface for Expo.
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import List

from main import <PERSON><PERSON><PERSON>
from config.settings import settings

logger = logging.getLogger(__name__)


def setup_cli_logging(verbose: bool = False):
    """Setup logging for CLI."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )


def process_files_cli(file_paths: List[str], output_dir: str = None) -> int:
    """
    Process files via CLI.
    
    Args:
        file_paths: List of file paths to process
        output_dir: Optional output directory override
        
    Returns:
        Exit code (0 for success, 1 for error)
    """
    try:
        # Override output directory if specified
        if output_dir:
            settings.output_directory = str(Path(output_dir).expanduser())
            settings.output_path.mkdir(parents=True, exist_ok=True)
        
        # Initialize app without GUI
        app = ExpoApp()
        
        # Validate files
        valid_files = []
        for file_path in file_paths:
            path = Path(file_path)
            if not path.exists():
                logger.error(f"File not found: {file_path}")
                continue
            if not settings.is_supported_file(str(path)):
                logger.error(f"Unsupported file type: {path.suffix}")
                continue
            valid_files.append(str(path))
        
        if not valid_files:
            logger.error("No valid files to process")
            return 1
        
        logger.info(f"Processing {len(valid_files)} files...")
        
        # Process files
        results = []
        for i, file_path in enumerate(valid_files):
            logger.info(f"Processing {i+1}/{len(valid_files)}: {Path(file_path).name}")
            
            try:
                result = app.process_single_file(file_path)
                if result:
                    results.append(result)
                    logger.info(f"✓ Generated: {Path(result).name}")
                else:
                    logger.warning(f"✗ Failed to process: {Path(file_path).name}")
            except Exception as e:
                logger.error(f"✗ Error processing {Path(file_path).name}: {e}")
        
        # Summary
        if results:
            logger.info(f"\n✓ Successfully processed {len(results)}/{len(valid_files)} files")
            logger.info(f"Output directory: {settings.output_directory}")
            return 0
        else:
            logger.error(f"\n✗ Failed to process any files")
            return 1
            
    except Exception as e:
        logger.error(f"CLI processing error: {e}")
        return 1


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Expo - Expedition Document Processor",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process single file
  python -m Expo.cli document.pdf
  
  # Process multiple files
  python -m Expo.cli doc1.pdf doc2.docx doc3.txt
  
  # Specify output directory
  python -m Expo.cli document.pdf -o ~/expedition_outputs
  
  # Verbose logging
  python -m Expo.cli document.pdf -v
        """
    )
    
    parser.add_argument(
        "files",
        nargs="+",
        help="Expedition document files to process (PDF, DOCX, DOC, TXT)"
    )
    
    parser.add_argument(
        "-o", "--output",
        help=f"Output directory (default: {settings.output_directory})"
    )
    
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Enable verbose logging"
    )
    
    parser.add_argument(
        "--gui",
        action="store_true",
        help="Launch GUI interface instead of CLI"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    setup_cli_logging(args.verbose)
    
    # Launch GUI if requested
    if args.gui:
        logger.info("Launching GUI interface...")
        try:
            app = ExpoApp()
            app.run()
            return 0
        except Exception as e:
            logger.error(f"GUI error: {e}")
            return 1
    
    # Process files via CLI
    return process_files_cli(args.files, args.output)


if __name__ == "__main__":
    sys.exit(main())
