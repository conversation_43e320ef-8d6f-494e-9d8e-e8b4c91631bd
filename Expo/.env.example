# Expo Configuration
# Copy this file to .env and customize as needed

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=mistral-7b-v0-1-gguf:latest

# Output Settings
OUTPUT_DIRECTORY=~/Desktop/output
JSON_INDENT=2

# Processing Settings
ENABLE_OCR=true
ENABLE_VALIDATION=true
MAX_FILE_SIZE_MB=100

# Agent Settings
AGENT_TIMEOUT_SECONDS=300
AGENT_MAX_RETRIES=3
AGENT_MAX_ITERATIONS=10

# Logging
LOG_LEVEL=INFO
LOG_FILE=expo.log
