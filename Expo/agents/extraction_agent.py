"""
LangChain agent for extracting expedition entities from documents.
"""

import json
import logging
from typing import Dict, Any, Optional, List
import re
from datetime import datetime

# LangChain imports
try:
    from langchain.agents import AgentExecutor, create_react_agent
    from langchain.prompts import PromptTemplate
    from langchain_ollama import OllamaLLM
    from langchain.tools import Tool
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logging.warning("LangChain not available. Install with: pip install langchain langchain-ollama")

from config.ollama_config import ollama_config
from tools.extraction_tools import create_extraction_tools

logger = logging.getLogger(__name__)


class ExtractionAgent:
    """
    LangChain agent for extracting expedition entities from document text.
    
    Extracts: dates, locations, tides, schedules, groups, equipment, etc.
    """
    
    def __init__(self):
        """Initialize the extraction agent."""
        self.llm = None
        self.agent_executor = None
        self._setup_agent()
    
    def _setup_agent(self):
        """Setup <PERSON><PERSON>hain agent with <PERSON>llama."""
        if not LANGCHAIN_AVAILABLE:
            logger.error("<PERSON><PERSON><PERSON><PERSON> is not available. Please install it.")
            return
        
        try:
            # Initialize Ollama LLM
            self.llm = OllamaLLM(
                base_url=ollama_config.base_url,
                model=ollama_config.model_name,
                **ollama_config.get_model_kwargs()
            )
            
            # Create extraction tools
            tools = create_extraction_tools()
            
            # Create agent prompt
            prompt = self._create_extraction_prompt()
            
            # Create ReAct agent
            agent = create_react_agent(
                llm=self.llm,
                tools=tools,
                prompt=prompt
            )
            
            # Create agent executor
            self.agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                verbose=True,
                **ollama_config.get_agent_kwargs()
            )
            
            logger.info("Extraction agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize extraction agent: {e}")
            self.agent_executor = None
    
    def _create_extraction_prompt(self) -> PromptTemplate:
        """Create the extraction prompt template."""
        template = """You are an expert at extracting expedition planning information from documents.

Your task is to extract structured information from expedition documents and return it as JSON.

IMPORTANT: When using tools, call them with the FULL document text as input, not just small fragments.

You have access to the following tools:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the full document text to analyze
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer as a JSON object with these fields:
{{
    "date": "YYYY-MM-DD",
    "weekday": "Monday/Tuesday/etc",
    "day_number": 1,
    "location": "Location Name",
    "utc_offset": "+00:00",
    "notes": "Equipment and setup notes (clean text, no control characters)",
    "zodiacs": 0,
    "twins": 0,
    "activity_type": "Activity description",
    "groups": [],
    "schedule": [],
    "tides": []
}}

Begin!

Question: {input}
Thought:{agent_scratchpad}"""

        return PromptTemplate(
            template=template,
            input_variables=["input", "agent_scratchpad"],
            partial_variables={
                "tools": "\n".join([f"{tool.name}: {tool.description}" for tool in create_extraction_tools()]),
                "tool_names": ", ".join([tool.name for tool in create_extraction_tools()])
            }
        )
    
    def extract_entities(self, document_text: str) -> Dict[str, Any]:
        """
        Extract expedition entities from document text.
        
        Args:
            document_text: Text content from expedition document
            
        Returns:
            Dictionary containing extracted entities
        """
        if not self.agent_executor:
            logger.error("Agent not available, falling back to regex extraction")
            return self._fallback_extraction(document_text)
        
        try:
            logger.info("Starting entity extraction with LangChain agent")

            # Validate input size
            if len(document_text.strip()) < 50:
                logger.warning("Document text too short for meaningful extraction")
                return self._fallback_extraction(document_text)

            # For now, skip the problematic agent and go directly to fallback
            # The agent has tool recognition issues that need more complex fixes
            logger.info("Using fallback extraction due to agent tool issues")
            return self._fallback_extraction(document_text)

        except Exception as e:
            logger.error(f"Agent extraction failed: {e}")
            return self._fallback_extraction(document_text)
    
    def _parse_agent_output(self, output: str) -> Dict[str, Any]:
        """Parse agent output to extract JSON."""
        try:
            # Try to find JSON in the output
            json_match = re.search(r'\{.*\}', output, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                logger.warning("No JSON found in agent output")
                return {}
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse agent JSON output: {e}")
            return {}
    
    def _fallback_extraction(self, document_text: str) -> Dict[str, Any]:
        """
        Fallback extraction using regex patterns.

        Args:
            document_text: Document text to extract from

        Returns:
            Dictionary with extracted entities
        """
        logger.info("Using fallback regex extraction")

        extracted = {
            "date": "",
            "weekday": "",
            "day_number": 1,
            "location": "",
            "utc_offset": "+00:00",
            "notes": "",
            "zodiacs": 0,
            "twins": 0,
            "activity_type": "",
            "groups": [],
            "schedule": [],
            "tides": []
        }

        # Extract date patterns (more comprehensive)
        date_patterns = [
            r'Date[:\s]*(\d{4}-\d{2}-\d{2})',
            r'(\d{4}-\d{2}-\d{2})',
            r'Date[:\s]*(\d{1,2}/\d{1,2}/\d{4})',
            r'(\d{1,2}/\d{1,2}/\d{4})',
            r'Date[:\s]*(\d{1,2}-\d{1,2}-\d{4})',
            r'(\d{1,2}-\d{1,2}-\d{4})'
        ]

        for pattern in date_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                extracted["date"] = match.group(1)
                break

        # Extract weekday
        weekday_pattern = r'\b(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)\b'
        weekday_match = re.search(weekday_pattern, document_text, re.IGNORECASE)
        if weekday_match:
            extracted["weekday"] = weekday_match.group(1).title()

        # Extract location (more comprehensive patterns)
        location_patterns = [
            r'Location[:\s]+([A-Za-z\s,.-]+?)(?:\n|$|Date|Day)',
            r'Site[:\s]+([A-Za-z\s,.-]+?)(?:\n|$|Date|Day)',
            r'Destination[:\s]+([A-Za-z\s,.-]+?)(?:\n|$|Date|Day)',
            r'at\s+([A-Z][A-Za-z\s,.-]+?)(?:\n|$|Date|Day)',
        ]

        for pattern in location_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                location = match.group(1).strip().strip(',.-')
                if len(location) > 2 and len(location) < 100:
                    extracted["location"] = location
                    break

        # Extract zodiac counts (more patterns)
        zodiac_patterns = [
            r'(\d+)\s*zodiac',
            r'zodiac[s]?[:\s]*(\d+)',
            r'(\d+)\s*Zodiac'
        ]

        for pattern in zodiac_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                extracted["zodiacs"] = int(match.group(1))
                break

        # Extract twin counts (more patterns)
        twin_patterns = [
            r'(\d+)\s*twin',
            r'twin[s]?[:\s]*(\d+)',
            r'(\d+)\s*Twin'
        ]

        for pattern in twin_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                extracted["twins"] = int(match.group(1))
                break

        # Extract group information
        group_patterns = [
            r'(Yellow|Blue|Red|Green|Orange|Purple)\s*[Gg]roup[:\s]*(\d{1,2}:\d{2})',
            r'(\d{1,2}:\d{2})\s*-\s*(Yellow|Blue|Red|Green|Orange|Purple)\s*[Gg]roup'
        ]

        for pattern in group_patterns:
            matches = re.findall(pattern, document_text, re.IGNORECASE)
            for match in matches:
                if len(match) == 2:
                    if ':' in match[0]:  # Time first
                        time, color = match
                    else:  # Color first
                        color, time = match

                    extracted["groups"].append({
                        "groupName": color.title(),
                        "color": color.title(),
                        "departureTime": time,
                        "activity": "Unknown"
                    })

        # Extract time patterns for schedule
        time_pattern = r'(\d{1,2}:\d{2})'
        times = re.findall(time_pattern, document_text)

        # Only add times to schedule if we don't have groups (to avoid duplication)
        if not extracted["groups"]:
            for time_str in times[:5]:  # Limit to first 5 times found
                extracted["schedule"].append({
                    "time": time_str,
                    "type": "event",
                    "description": f"Scheduled event at {time_str}",
                    "location": extracted["location"]
                })

        # Extract tide information
        tide_patterns = [
            r'(\d{1,2}:\d{2})\s*-\s*(?:Low|High)\s*Tide\s*\(([0-9.]+)m?\)',
            r'(?:Low|High)\s*Tide[:\s]*(\d{1,2}:\d{2})\s*[:\-\s]*([0-9.]+)m?',
            r'(\d{1,2}:\d{2})\s*(?:Low|High)\s*Tide\s*([0-9.]+)m?'
        ]

        for pattern in tide_patterns:
            matches = re.findall(pattern, document_text, re.IGNORECASE)
            for match in matches:
                if len(match) == 2:
                    time, height = match
                    try:
                        height_float = float(height)
                        tide_type = "Low Tide" if "low" in document_text.lower() else "High Tide"
                        extracted["tides"].append({
                            "time": time,
                            "height": height_float,
                            "label": tide_type
                        })
                    except ValueError:
                        continue

        # Extract activity type
        activity_patterns = [
            r'Activity[:\s]+([A-Za-z\s]+)',
            r'Operation[:\s]+([A-Za-z\s]+)',
            r'(\d+h?\s*[A-Za-z\s]*(?:Cruise|Landing|Operation))'
        ]

        for pattern in activity_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                activity = match.group(1).strip()
                if len(activity) > 2:
                    extracted["activity_type"] = activity
                    break

        # Extract notes from equipment section
        equipment_section = re.search(r'Equipment[:\s]*([^.]*(?:\.[^.]*)*)', document_text, re.IGNORECASE | re.DOTALL)
        if equipment_section:
            notes = equipment_section.group(1).strip()
            # Clean up notes
            notes = re.sub(r'\s+', ' ', notes)
            if len(notes) > 10:
                extracted["notes"] = notes[:200]  # Limit length

        logger.info(f"Fallback extraction completed for location: '{extracted['location']}', date: '{extracted['date']}'")
        return extracted
    
    def is_available(self) -> bool:
        """Check if extraction agent is ready to use."""
        return LANGCHAIN_AVAILABLE and self.agent_executor is not None
