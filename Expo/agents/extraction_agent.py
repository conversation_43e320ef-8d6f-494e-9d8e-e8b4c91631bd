"""
LangChain agent for extracting expedition entities from documents.
"""

import json
import logging
from typing import Dict, Any, Optional, List
import re
from datetime import datetime

# LangChain imports
try:
    from langchain.agents import AgentExecutor, create_react_agent
    from langchain.prompts import PromptTemplate
    from langchain_ollama import OllamaLLM
    from langchain.tools import Tool
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logging.warning("LangChain not available. Install with: pip install langchain langchain-ollama")

from config.ollama_config import ollama_config
from tools.extraction_tools import create_extraction_tools

logger = logging.getLogger(__name__)


class ExtractionAgent:
    """
    LangChain agent for extracting expedition entities from document text.
    
    Extracts: dates, locations, tides, schedules, groups, equipment, etc.
    """
    
    def __init__(self):
        """Initialize the extraction agent."""
        self.llm = None
        self.agent_executor = None
        self._setup_agent()
    
    def _setup_agent(self):
        """Setup <PERSON><PERSON>hain agent with <PERSON>llama."""
        if not LANGCHAIN_AVAILABLE:
            logger.error("<PERSON><PERSON><PERSON><PERSON> is not available. Please install it.")
            return
        
        try:
            # Initialize Ollama LLM
            self.llm = OllamaLLM(
                base_url=ollama_config.base_url,
                model=ollama_config.model_name,
                **ollama_config.get_model_kwargs()
            )
            
            # Create extraction tools
            tools = create_extraction_tools()
            
            # Create agent prompt
            prompt = self._create_extraction_prompt()
            
            # Create ReAct agent
            agent = create_react_agent(
                llm=self.llm,
                tools=tools,
                prompt=prompt
            )
            
            # Create agent executor
            self.agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                verbose=True,
                **ollama_config.get_agent_kwargs()
            )
            
            logger.info("Extraction agent initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize extraction agent: {e}")
            self.agent_executor = None
    
    def _create_extraction_prompt(self) -> PromptTemplate:
        """Create the extraction prompt template."""
        template = """You are an expert at extracting expedition planning information from documents.

Your task is to extract structured information from expedition documents and return it as JSON.

Extract the following information:
- Date and day information (date, weekday, day_number)
- Location details (location name, coordinates if available)
- Schedule information (arrival times, departure times, activities)
- Group information (group names, colors, departure/return times)
- Tide information (times and heights)
- Equipment and logistics (zodiacs, twins, notes)
- Activity types and descriptions

You have access to the following tools:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer as a JSON object

Begin!

Question: {input}
Thought:{agent_scratchpad}"""
        
        return PromptTemplate(
            template=template,
            input_variables=["input", "agent_scratchpad"],
            partial_variables={
                "tools": "\n".join([f"{tool.name}: {tool.description}" for tool in create_extraction_tools()]),
                "tool_names": ", ".join([tool.name for tool in create_extraction_tools()])
            }
        )
    
    def extract_entities(self, document_text: str) -> Dict[str, Any]:
        """
        Extract expedition entities from document text.
        
        Args:
            document_text: Text content from expedition document
            
        Returns:
            Dictionary containing extracted entities
        """
        if not self.agent_executor:
            logger.error("Agent not available, falling back to regex extraction")
            return self._fallback_extraction(document_text)
        
        try:
            logger.info("Starting entity extraction with LangChain agent")
            
            # Prepare input for agent
            input_text = f"""
            Extract expedition planning information from the following document text:
            
            {document_text[:4000]}  # Limit text length for processing
            
            Return the extracted information as a JSON object with the following structure:
            {{
                "date": "YYYY-MM-DD",
                "weekday": "Monday/Tuesday/etc",
                "day_number": 1,
                "location": "Location Name",
                "utc_offset": "+00:00",
                "notes": "Equipment and setup notes",
                "zodiacs": 8,
                "twins": 1,
                "activity_type": "Activity description",
                "groups": [
                    {{
                        "groupName": "Yellow",
                        "color": "Yellow", 
                        "departureTime": "07:40",
                        "returnTime": "09:40",
                        "activity": "Zodiac Cruise"
                    }}
                ],
                "schedule": [
                    {{
                        "time": "07:40",
                        "type": "arrival",
                        "description": "Scheduled arrival",
                        "location": "Location"
                    }}
                ],
                "tides": [
                    {{
                        "time": "02:00",
                        "height": 2.0,
                        "label": "Low Tide"
                    }}
                ]
            }}
            """
            
            # Run agent
            result = self.agent_executor.invoke({"input": input_text})
            
            # Parse result
            if "output" in result:
                return self._parse_agent_output(result["output"])
            else:
                logger.warning("No output from agent, using fallback")
                return self._fallback_extraction(document_text)
                
        except Exception as e:
            logger.error(f"Agent extraction failed: {e}")
            return self._fallback_extraction(document_text)
    
    def _parse_agent_output(self, output: str) -> Dict[str, Any]:
        """Parse agent output to extract JSON."""
        try:
            # Try to find JSON in the output
            json_match = re.search(r'\{.*\}', output, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                logger.warning("No JSON found in agent output")
                return {}
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse agent JSON output: {e}")
            return {}
    
    def _fallback_extraction(self, document_text: str) -> Dict[str, Any]:
        """
        Fallback extraction using regex patterns.
        
        Args:
            document_text: Document text to extract from
            
        Returns:
            Dictionary with extracted entities
        """
        logger.info("Using fallback regex extraction")
        
        extracted = {
            "date": "",
            "weekday": "",
            "day_number": 1,
            "location": "",
            "utc_offset": "+00:00",
            "notes": "",
            "zodiacs": 0,
            "twins": 0,
            "activity_type": "",
            "groups": [],
            "schedule": [],
            "tides": []
        }
        
        # Extract date patterns
        date_patterns = [
            r'(\d{4}-\d{2}-\d{2})',
            r'(\d{1,2}/\d{1,2}/\d{4})',
            r'(\d{1,2}-\d{1,2}-\d{4})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, document_text)
            if match:
                extracted["date"] = match.group(1)
                break
        
        # Extract location (look for common location indicators)
        location_patterns = [
            r'Location[:\s]+([A-Za-z\s]+)',
            r'Site[:\s]+([A-Za-z\s]+)',
            r'Destination[:\s]+([A-Za-z\s]+)'
        ]
        
        for pattern in location_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                extracted["location"] = match.group(1).strip()
                break
        
        # Extract zodiac counts
        zodiac_match = re.search(r'(\d+)\s*zodiac', document_text, re.IGNORECASE)
        if zodiac_match:
            extracted["zodiacs"] = int(zodiac_match.group(1))
        
        # Extract twin counts
        twin_match = re.search(r'(\d+)\s*twin', document_text, re.IGNORECASE)
        if twin_match:
            extracted["twins"] = int(twin_match.group(1))
        
        # Extract time patterns for schedule
        time_pattern = r'(\d{1,2}:\d{2})'
        times = re.findall(time_pattern, document_text)
        
        for time_str in times[:5]:  # Limit to first 5 times found
            extracted["schedule"].append({
                "time": time_str,
                "type": "event",
                "description": f"Scheduled event at {time_str}",
                "location": extracted["location"]
            })
        
        logger.info(f"Fallback extraction completed for location: {extracted['location']}")
        return extracted
    
    def is_available(self) -> bool:
        """Check if extraction agent is ready to use."""
        return LANGCHAIN_AVAILABLE and self.agent_executor is not None
