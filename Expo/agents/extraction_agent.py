"""
LangChain agent for extracting expedition entities from documents.
"""

import json
import logging
from typing import Dict, Any, Optional, List
import re
from datetime import datetime
import asyncio
from concurrent.futures import Thread<PERSON>ool<PERSON>xecutor, TimeoutError

# LangChain imports
try:
    from langchain.agents import Agent<PERSON>xecutor, create_react_agent
    from langchain.prompts import PromptTemplate
    from langchain_ollama import OllamaLLM
    from langchain.tools import Tool
    from langchain.schema import AgentAction, AgentFinish
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    AgentExecutor = None
    create_react_agent = None
    PromptTemplate = None
    OllamaLLM = None
    Tool = None
    logging.warning("LangChain not available. Install with: pip install langchain langchain-ollama")

from config.ollama_config import ollama_config
from tools.extraction_tools import create_extraction_tools
from core.token_manager import TokenManager

logger = logging.getLogger(__name__)


class ExtractionAgent:
    """
    LangChain agent for extracting expedition entities from document text.
    
    Extracts: dates, locations, tides, schedules, groups, equipment, etc.
    """
    
    def __init__(self, timeout: int = 90, max_iterations: int = 8):
        """
        Initialize the extraction agent.
        
        Args:
            timeout: Maximum time to wait for agent response (seconds)
            max_iterations: Maximum number of agent iterations
        """
        self.llm = None
        self.agent_executor = None
        self.timeout = timeout
        self.max_iterations = max_iterations

        # Initialize token manager
        self.token_manager = TokenManager(ollama_config.model_name)

        self._setup_agent()
    
    def _setup_agent(self):
        """Setup LangChain agent with Ollama."""
        if not LANGCHAIN_AVAILABLE:
            logger.error("LangChain is not available. Please install it.")
            return
        
        try:
            # Initialize Ollama LLM with better configuration
            model_kwargs = ollama_config.get_model_kwargs()
            model_kwargs['temperature'] = 0.1  # Override for consistent extraction

            self.llm = OllamaLLM(
                base_url=ollama_config.base_url,
                model=ollama_config.model_name,
                **model_kwargs
            )
            
            # Skip connection test for faster initialization
            logger.info("LLM initialized, skipping connection test")
            
            # Create extraction tools
            tools = create_extraction_tools()
            if not tools:
                logger.warning("No extraction tools available, creating basic tools")
                tools = self._create_basic_tools()
            
            # Create the prompt template
            prompt = self._create_extraction_prompt()
            
            # Create the ReAct agent
            agent = create_react_agent(
                llm=self.llm,
                tools=tools,
                prompt=prompt
            )

            # Create the agent executor with proper configuration
            self.agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                verbose=True,
                handle_parsing_errors=True,
                max_iterations=self.max_iterations,
                max_execution_time=self.timeout,
                return_intermediate_steps=True
            )

            logger.info("Extraction agent initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize extraction agent: {e}")
            self.llm = None
            self.agent_executor = None
    
    def _create_basic_tools(self) -> List[Any]:
        """Create basic extraction tools if none are available."""
        def extract_dates_tool(text: str) -> str:
            """Extract dates from text."""
            date_patterns = [
                r'\d{4}-\d{2}-\d{2}',
                r'\d{1,2}/\d{1,2}/\d{4}',
                r'\d{1,2}-\d{1,2}-\d{4}'
            ]
            dates = []
            for pattern in date_patterns:
                matches = re.findall(pattern, text)
                dates.extend(matches)
            return json.dumps({"dates": dates})
        
        def extract_locations_tool(text: str) -> str:
            """Extract locations from text."""
            location_patterns = [
                r'Location[:\s]+([A-Za-z\s,.-]+?)(?:\n|$|Date|Day)',
                r'Site[:\s]+([A-Za-z\s,.-]+?)(?:\n|$|Date|Day)',
            ]
            locations = []
            for pattern in location_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                locations.extend([m.strip() for m in matches])
            return json.dumps({"locations": locations})
        
        def extract_equipment_tool(text: str) -> str:
            """Extract equipment information from text."""
            equipment_patterns = [
                r'(\d+)\s*zodiac',
                r'(\d+)\s*twin',
                r'Equipment[:\s]*([^.]*)'
            ]
            equipment = {}
            for pattern in equipment_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if 'zodiac' in pattern:
                    equipment['zodiacs'] = [int(m) for m in matches if m.isdigit()]
                elif 'twin' in pattern:
                    equipment['twins'] = [int(m) for m in matches if m.isdigit()]
                else:
                    equipment['notes'] = matches
            return json.dumps(equipment)
        
        return [
            Tool(
                name="extract_dates",
                description="Extract dates from document text",
                func=extract_dates_tool
            ),
            Tool(
                name="extract_locations", 
                description="Extract location information from document text",
                func=extract_locations_tool
            ),
            Tool(
                name="extract_equipment",
                description="Extract equipment and zodiac information from document text", 
                func=extract_equipment_tool
            )
        ]
    
    def _create_extraction_prompt(self) -> Any:
        """Create the extraction prompt template."""
        template = """You are an expert at extracting expedition planning information from documents.

Extract structured information from expedition documents and return it as JSON.

You have access to these tools:
{tools}

IMPORTANT INSTRUCTIONS:
1. Analyze the document text provided in the input
2. Use available tools to extract different types of information
3. Always pass the complete document text to each tool
4. Combine all tool results into a complete JSON response
5. Return ONLY a valid JSON object as your Final Answer
6. If extraction fails, return a JSON with empty/default values

Expected JSON structure:
{{
    "date": "YYYY-MM-DD or empty string",
    "weekday": "Monday/Tuesday/etc or empty string", 
    "day_number": 1,
    "location": "Location Name or empty string",
    "utc_offset": "+00:00",
    "notes": "Equipment and setup notes or empty string",
    "zodiacs": 0,
    "twins": 0,
    "activity_type": "Activity description or empty string",
    "groups": [],
    "schedule": [],
    "tides": []
}}

Use this format:

Question: {input}
Thought: I need to extract expedition information from this document text.
Action: extract_dates
Action Input: {input}
Observation: [tool result]
Thought: Now I'll extract location information.
Action: extract_locations  
Action Input: {input}
Observation: [tool result]
Thought: Let me get equipment information.
Action: extract_equipment
Action Input: {input}
Observation: [tool result]
Thought: I have all the information needed to create the final JSON response.
Final Answer: [VALID JSON ONLY]

Begin!

Question: {input}
Thought:{agent_scratchpad}"""

        tools = create_extraction_tools() or self._create_basic_tools()

        return PromptTemplate(
            template=template,
            input_variables=["input", "agent_scratchpad"],
            partial_variables={
                "tools": "\n".join([f"{tool.name}: {tool.description}" for tool in tools]),
                "tool_names": ", ".join([tool.name for tool in tools])
            }
        )
    
    def extract_entities(self, document_text: str) -> Dict[str, Any]:
        """
        Extract expedition entities from document text.
        
        Args:
            document_text: Text content from expedition document
            
        Returns:
            Dictionary containing extracted entities
        """
        # Input validation
        if not document_text or not document_text.strip():
            logger.warning("Empty document text provided")
            return self._get_empty_result()
        
        if not self.is_available():
            logger.error("Agent not available, falling back to regex extraction")
            return self._fallback_extraction(document_text)
        
        try:
            logger.info("Starting entity extraction with LangChain agent")

            # Validate input size
            if len(document_text.strip()) < 20:
                logger.warning("Document text too short for meaningful extraction")
                return self._fallback_extraction(document_text)

            # Use token manager for intelligent text preparation
            within_limit, estimated_tokens = self.token_manager.check_token_limit(document_text)

            if not within_limit:
                logger.info(f"Document exceeds token limit ({estimated_tokens} tokens), applying semantic slicing")
                processed_text = self.token_manager.semantic_slice(document_text)
            else:
                logger.info(f"Document within token limit: {estimated_tokens} tokens")
                processed_text = self._preprocess_text(document_text, 4000)

            # Run agent with timeout using thread executor
            with ThreadPoolExecutor(max_workers=1) as executor:
                try:
                    future = executor.submit(self._run_agent, processed_text)
                    result = future.result(timeout=self.timeout)
                    
                    if result and "output" in result:
                        parsed_result = self._parse_agent_output(result["output"])
                        if self._validate_result(parsed_result):
                            logger.info("Agent extraction successful")
                            return parsed_result
                        else:
                            logger.warning("Agent output invalid, using fallback")
                            return self._fallback_extraction(document_text)
                    else:
                        logger.warning("No valid output from agent, using fallback")
                        return self._fallback_extraction(document_text)
                        
                except TimeoutError:
                    logger.error(f"Agent execution timed out after {self.timeout} seconds")
                    return self._fallback_extraction(document_text)

        except Exception as e:
            logger.error(f"Agent extraction failed: {e}")
            return self._fallback_extraction(document_text)
    
    def _preprocess_text(self, text: str, limit: int) -> str:
        """Preprocess text for better extraction."""
        # Clean up text
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Truncate if too long
        if len(text) > limit:
            # Try to truncate at sentence boundary
            truncated = text[:limit]
            last_period = truncated.rfind('.')
            if last_period > limit * 0.8:  # If period is in last 20%
                text = truncated[:last_period + 1]
            else:
                text = truncated
        
        return text
    
    def _run_agent(self, text: str) -> Dict[str, Any]:
        """Run the agent with the provided text and aggregate outputs."""
        response = self.agent_executor.invoke({"input": text})
        if response and "intermediate_steps" in response:
            steps = response["intermediate_steps"]
            outputs = [step[1] for step in steps if isinstance(step, tuple) and isinstance(step[1], dict)]
            aggregated = self._aggregate_tool_outputs(outputs)
            return {"output": aggregated}
        return {}
    
    def _validate_result(self, result: Dict[str, Any]) -> bool:
        """Validate that the result has meaningful content."""
        if not isinstance(result, dict):
            return False
        
        # Check if at least one meaningful field is populated
        meaningful_fields = ["location", "date", "zodiacs", "twins", "groups", "schedule", "tides"]
        for field in meaningful_fields:
            if field in result:
                value = result[field]
                if value and value != "" and value != 0 and value != []:
                    return True
        
        return False
    
    def _get_empty_result(self) -> Dict[str, Any]:
        """Return empty result structure."""
        return {
            "date": "",
            "weekday": "",
            "day_number": 1,
            "location": "",
            "utc_offset": "+00:00",
            "notes": "",
            "zodiacs": 0,
            "twins": 0,
            "activity_type": "",
            "groups": [],
            "schedule": [],
            "tides": []
        }

    def _parse_agent_output(self, output: str) -> Dict[str, Any]:
        """Parse agent output to extract JSON."""
        try:
            # Clean the output first
            output = output.strip()

            # Remove common prefixes/suffixes
            output = re.sub(r'^.*?Final Answer:\s*', '', output, flags=re.DOTALL | re.IGNORECASE)
            output = re.sub(r'^.*?```json\s*', '', output, flags=re.DOTALL | re.IGNORECASE)
            output = re.sub(r'```.*$', '', output, flags=re.DOTALL)

            # Try to find and parse JSON
            json_patterns = [
                r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # Nested JSON
                r'\{.*?\}',  # Simple JSON
            ]

            for pattern in json_patterns:
                json_matches = re.findall(pattern, output, re.DOTALL)
                for json_str in json_matches:
                    try:
                        json_str = json_str.strip()
                        if json_str.endswith('}'):
                            parsed = json.loads(json_str)
                            if isinstance(parsed, dict):
                                # Ensure all required fields exist
                                result = self._get_empty_result()
                                result.update(parsed)
                                logger.info("Successfully parsed agent JSON output")
                                return result
                    except json.JSONDecodeError:
                        continue

            # If no valid JSON found, extract from text
            logger.warning("No valid JSON found, attempting text extraction")
            return self._extract_from_text(output)

        except Exception as e:
            logger.error(f"Failed to parse agent output: {e}")
            return self._get_empty_result()

    def _aggregate_tool_outputs(self, outputs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Aggregate outputs from tools into the final structured result."""
        result = self._get_empty_result()
        for output in outputs:
            if isinstance(output, dict):
                result.update({k: v for k, v in output.items() if v})
        return result

    def _extract_from_text(self, text: str) -> Dict[str, Any]:
        """Extract key information from plain text output."""
        result = self._get_empty_result()

        # Extract basic information from text
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if 'location' in line.lower() and ':' in line:
                location = line.split(':', 1)[1].strip().strip('"\'')
                if location:
                    result["location"] = location
            elif 'date' in line.lower() and ':' in line:
                date = line.split(':', 1)[1].strip().strip('"\'')
                if date:
                    result["date"] = date
            elif 'zodiac' in line.lower() and any(char.isdigit() for char in line):
                numbers = re.findall(r'\d+', line)
                if numbers:
                    result["zodiacs"] = int(numbers[0])

        return result
    
    def _fallback_extraction(self, document_text: str) -> Dict[str, Any]:
        """
        Fallback extraction using regex patterns.

        Args:
            document_text: Document text to extract from

        Returns:
            Dictionary with extracted entities
        """
        logger.info("Using fallback regex extraction")

        extracted = self._get_empty_result()

        # Extract date patterns (enhanced for various formats)
        date_patterns = [
            # Standard formats
            r'Date[:\s]*(\d{4}-\d{2}-\d{2})',
            r'(\d{4}-\d{2}-\d{2})',
            r'Date[:\s]*(\d{1,2}/\d{1,2}/\d{4})',
            r'(\d{1,2}/\d{1,2}/\d{4})',
            r'Date[:\s]*(\d{1,2}-\d{1,2}-\d{4})',
            r'(\d{1,2}-\d{1,2}-\d{4})',
            # Ordinal dates like "17th March"
            r'(\d{1,2}(?:st|nd|rd|th)\s+(?:January|February|March|April|May|June|July|August|September|October|November|December))',
            r'(\d{1,2}(?:st|nd|rd|th)\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec))',
            # Month day formats
            r'((?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2}(?:st|nd|rd|th)?)',
            r'((?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{1,2}(?:st|nd|rd|th)?)'
        ]

        for pattern in date_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                date_str = match.group(1)
                # Convert ordinal dates to standard format
                if re.search(r'\d{1,2}(?:st|nd|rd|th)', date_str):
                    extracted["date"] = self._normalize_date(date_str)
                else:
                    extracted["date"] = date_str
                break

        # Extract weekday
        weekday_pattern = r'\b(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)\b'
        weekday_match = re.search(weekday_pattern, document_text, re.IGNORECASE)
        if weekday_match:
            extracted["weekday"] = weekday_match.group(1).title()

        # Extract location (improved patterns to avoid crew names)
        location_patterns = [
            # Explicit location markers
            r'Location[:\s]+([A-Za-z\s,.-–—]+?)(?:\n|$|Date|Day|Time)',
            r'Site[:\s]+([A-Za-z\s,.-–—]+?)(?:\n|$|Date|Day|Time)',
            r'Destination[:\s]+([A-Za-z\s,.-–—]+?)(?:\n|$|Date|Day|Time)',
            r'Island[:\s]+([A-Za-z\s,.-–—]+?)(?:\n|$|Date|Day|Time)',
            r'Port[:\s]+([A-Za-z\s,.-–—]+?)(?:\n|$|Date|Day|Time)',
            # Japanese location patterns
            r'([A-Za-z]+jima)\b',  # Matches -jima (island in Japanese)
            r'([A-Za-z]+shima)\b',  # Matches -shima (island in Japanese)
            # Geographic patterns with dashes/em-dashes
            r'([A-Z][a-z]+(?:\s*[–—-]\s*[A-Z][a-z]+)+)',
            # At location patterns (but avoid crew lists)
            r'at\s+([A-Z][A-Za-z\s,.-–—]+?)(?:\n|$|Date|Day|Time)',
        ]

        for pattern in location_patterns:
            matches = re.findall(pattern, document_text, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    location = match[0] if match[0] else match[1] if len(match) > 1 else ""
                else:
                    location = match

                location = location.strip().strip(',.-')

                # Validate location (avoid crew names)
                if (2 < len(location) < 100 and
                    not self._looks_like_crew_names(location) and
                    not re.search(r'\b(crew|team|staff|guide|driver)\b', location, re.IGNORECASE)):
                    extracted["location"] = location
                    break

            if extracted["location"]:
                break

        # Extract zodiac counts
        zodiac_patterns = [
            r'(\d+)\s*zodiac',
            r'zodiac[s]?[:\s]*(\d+)',
            r'(\d+)\s*Zodiac'
        ]

        for pattern in zodiac_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                extracted["zodiacs"] = int(match.group(1))
                break

        # Extract twin counts
        twin_patterns = [
            r'(\d+)\s*twin',
            r'twin[s]?[:\s]*(\d+)',
            r'(\d+)\s*Twin'
        ]

        for pattern in twin_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                extracted["twins"] = int(match.group(1))
                break

        # Extract group information
        group_patterns = [
            r'(Yellow|Blue|Red|Green|Orange|Purple)\s*[Gg]roup[:\s]*(\d{1,2}:\d{2})',
            r'(\d{1,2}:\d{2})\s*-\s*(Yellow|Blue|Red|Green|Orange|Purple)\s*[Gg]roup'
        ]

        for pattern in group_patterns:
            matches = re.findall(pattern, document_text, re.IGNORECASE)
            for match in matches:
                if len(match) == 2:
                    if ':' in match[0]:  # Time first
                        time, color = match
                    else:  # Color first
                        color, time = match

                    extracted["groups"].append({
                        "groupName": color.title(),
                        "color": color.title(),
                        "departureTime": time,
                        "activity": "Unknown"
                    })

        # Extract schedule from times (if no groups found)
        if not extracted["groups"]:
            time_pattern = r'(\d{1,2}:\d{2})'
            times = re.findall(time_pattern, document_text)
            for time_str in times[:5]:
                extracted["schedule"].append({
                    "time": time_str,
                    "type": "event",
                    "description": f"Scheduled event at {time_str}",
                    "location": extracted["location"]
                })

        # Extract tide information
        tide_patterns = [
            r'(\d{1,2}:\d{2})\s*-\s*(?:Low|High)\s*Tide\s*\(([0-9.]+)m?\)',
            r'(?:Low|High)\s*Tide[:\s]*(\d{1,2}:\d{2})\s*[:\-\s]*([0-9.]+)m?'
        ]

        for pattern in tide_patterns:
            matches = re.findall(pattern, document_text, re.IGNORECASE)
            for match in matches:
                if len(match) == 2:
                    time, height = match
                    try:
                        height_float = float(height)
                        tide_type = "Low Tide" if "low" in document_text.lower() else "High Tide"
                        extracted["tides"].append({
                            "time": time,
                            "height": height_float,
                            "label": tide_type
                        })
                    except ValueError:
                        continue

        # Extract activity type
        activity_patterns = [
            r'Activity[:\s]+([A-Za-z\s]+)',
            r'Operation[:\s]+([A-Za-z\s]+)',
            r'(\d+h?\s*[A-Za-z\s]*(?:Cruise|Landing|Operation))'
        ]

        for pattern in activity_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                activity = match.group(1).strip()
                if len(activity) > 2:
                    extracted["activity_type"] = activity
                    break

        # Extract notes from equipment section
        equipment_section = re.search(r'Equipment[:\s]*([^.]*(?:\.[^.]*)*)', document_text, re.IGNORECASE | re.DOTALL)
        if equipment_section:
            notes = equipment_section.group(1).strip()
            notes = re.sub(r'\s+', ' ', notes)
            if len(notes) > 10:
                extracted["notes"] = notes[:200]

        logger.info(f"Fallback extraction completed for location: '{extracted['location']}', date: '{extracted['date']}'")
        return extracted

    def _normalize_date(self, date_str: str) -> str:
        """Convert ordinal dates like '17th March' to standard format."""
        import datetime

        # Remove ordinal suffixes
        date_str = re.sub(r'(\d+)(?:st|nd|rd|th)', r'\1', date_str)

        # Month name mappings
        month_map = {
            'january': '01', 'jan': '01',
            'february': '02', 'feb': '02',
            'march': '03', 'mar': '03',
            'april': '04', 'apr': '04',
            'may': '05', 'may': '05',
            'june': '06', 'jun': '06',
            'july': '07', 'jul': '07',
            'august': '08', 'aug': '08',
            'september': '09', 'sep': '09',
            'october': '10', 'oct': '10',
            'november': '11', 'nov': '11',
            'december': '12', 'dec': '12'
        }

        try:
            # Parse day and month
            parts = date_str.lower().split()
            if len(parts) == 2:
                day = parts[0].zfill(2)
                month_name = parts[1]
                month = month_map.get(month_name, '01')

                # Assume current year if not specified
                current_year = datetime.datetime.now().year
                return f"{current_year}-{month}-{day}"
        except:
            pass

        return date_str  # Return original if parsing fails

    def _looks_like_crew_names(self, text: str) -> bool:
        """Check if text looks like a list of crew names."""
        # Split by common separators
        names = re.split(r'[,\s]+', text.strip())

        # If we have multiple short words (likely names)
        if len(names) >= 2:
            short_names = [name for name in names if 2 <= len(name) <= 10 and name.isalpha()]
            if len(short_names) >= 2:
                return True

        # Check for common crew name patterns
        crew_patterns = [
            r'\b[A-Z][a-z]{2,8}(?:\s*,\s*[A-Z][a-z]{2,8}){2,}',  # Multiple capitalized names
            r'\b(?:Ryo|Xav|Katsu|Eli|Alex|Sam|Tom|Ben|Dan|Max)\b',  # Common short names
        ]

        for pattern in crew_patterns:
            if re.search(pattern, text):
                return True

        return False

    def is_available(self) -> bool:
        """Check if extraction agent is ready to use."""
        return LANGCHAIN_AVAILABLE and self.agent_executor is not None
    
    def health_check(self) -> Dict[str, Any]:
        """Perform a health check on the agent."""
        status = {
            "langchain_available": LANGCHAIN_AVAILABLE,
            "llm_initialized": self.llm is not None,
            "agent_initialized": self.agent_executor is not None,
            "ready": self.is_available()
        }
        
        if self.llm:
            try:
                test_response = self.llm.invoke("Hello")
                status["llm_responsive"] = True
            except Exception as e:
                status["llm_responsive"] = False
                status["llm_error"] = str(e)
        
        return status
