"""
LangChain agent for extracting expedition entities from documents.
"""

import json
import logging
from typing import Dict, Any, Optional, List
import re
from datetime import datetime
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor, TimeoutError

# Lang<PERSON>hain imports
try:
    from langchain.agents import RunnableAgentExecutor, create_structured_chat_agent
    from langchain.prompts import PromptTemplate
    from langchain_ollama import OllamaLLM
    from langchain.tools import Tool
    from langchain.schema import AgentAction, AgentFinish
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    AgentExecutor = None
    create_react_agent = None
    PromptTemplate = None
    OllamaLLM = None
    Tool = None
    logging.warning("Lang<PERSON>hain not available. Install with: pip install langchain langchain-ollama")

from config.ollama_config import ollama_config
from tools.extraction_tools import create_extraction_tools

logger = logging.getLogger(__name__)


class ExtractionAgent:
    """
    LangChain agent for extracting expedition entities from document text.
    
    Extracts: dates, locations, tides, schedules, groups, equipment, etc.
    """
    
    def __init__(self, timeout: int = 30, max_iterations: int = 10):
        """
        Initialize the extraction agent.
        
        Args:
            timeout: Maximum time to wait for agent response (seconds)
            max_iterations: Maximum number of agent iterations
        """
        self.llm = None
        self.agent_executor = None
        self.timeout = timeout
        self.max_iterations = max_iterations
        self._setup_agent()
    
    def _setup_agent(self):
        """Setup LangChain agent with Ollama."""
        if not LANGCHAIN_AVAILABLE:
            logger.error("LangChain is not available. Please install it.")
            return
        
        try:
            # Initialize Ollama LLM with better configuration
            model_kwargs = ollama_config.get_model_kwargs()
            model_kwargs['temperature'] = 0.1  # Override for consistent extraction

            self.llm = OllamaLLM(
                base_url=ollama_config.base_url,
                model=ollama_config.model_name,
                **model_kwargs
            )
            
            # Skip connection test for faster initialization
            logger.info("LLM initialized, skipping connection test")
            
            # Create extraction tools
            tools = create_extraction_tools()
            if not tools:
                logger.warning("No extraction tools available, creating basic tools")
                tools = self._create_basic_tools()
            
            # Create the prompt template
            prompt = self._create_extraction_prompt()
            
            # Create the structured chat agent
            agent = create_structured_chat_agent(
                llm=self.llm,
                tools=tools,
                prompt=prompt
            )

            # Create the agent executor with proper configuration
            self.agent_executor = RunnableAgentExecutor(
                agent=agent,
                tools=tools,
                verbose=True,
                max_iterations=self.max_iterations,
                max_execution_time=self.timeout,
                return_intermediate_steps=True
            )

            logger.info("Extraction agent initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize extraction agent: {e}")
            self.llm = None
            self.agent_executor = None
    
    def _create_basic_tools(self) -> List[Any]:
        """Create basic extraction tools if none are available."""
        def extract_dates_tool(text: str) -> str:
            """Extract dates from text."""
            date_patterns = [
                r'\d{4}-\d{2}-\d{2}',
                r'\d{1,2}/\d{1,2}/\d{4}',
                r'\d{1,2}-\d{1,2}-\d{4}'
            ]
            dates = []
            for pattern in date_patterns:
                matches = re.findall(pattern, text)
                dates.extend(matches)
            return json.dumps({"dates": dates})
        
        def extract_locations_tool(text: str) -> str:
            """Extract locations from text."""
            location_patterns = [
                r'Location[:\s]+([A-Za-z\s,.-]+?)(?:\n|$|Date|Day)',
                r'Site[:\s]+([A-Za-z\s,.-]+?)(?:\n|$|Date|Day)',
            ]
            locations = []
            for pattern in location_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                locations.extend([m.strip() for m in matches])
            return json.dumps({"locations": locations})
        
        def extract_equipment_tool(text: str) -> str:
            """Extract equipment information from text."""
            equipment_patterns = [
                r'(\d+)\s*zodiac',
                r'(\d+)\s*twin',
                r'Equipment[:\s]*([^.]*)'
            ]
            equipment = {}
            for pattern in equipment_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if 'zodiac' in pattern:
                    equipment['zodiacs'] = [int(m) for m in matches if m.isdigit()]
                elif 'twin' in pattern:
                    equipment['twins'] = [int(m) for m in matches if m.isdigit()]
                else:
                    equipment['notes'] = matches
            return json.dumps(equipment)
        
        return [
            Tool(
                name="extract_dates",
                description="Extract dates from document text",
                func=extract_dates_tool
            ),
            Tool(
                name="extract_locations", 
                description="Extract location information from document text",
                func=extract_locations_tool
            ),
            Tool(
                name="extract_equipment",
                description="Extract equipment and zodiac information from document text", 
                func=extract_equipment_tool
            )
        ]
    
    def _create_extraction_prompt(self) -> Any:
        """Create the extraction prompt template."""
        template = """You are an expert at extracting expedition planning information from documents.

Extract structured information from expedition documents and return it as JSON.

You have access to these tools:
{tools}

IMPORTANT INSTRUCTIONS:
1. Analyze the document text provided in the input
2. Use available tools to extract different types of information
3. Always pass the complete document text to each tool
4. Combine all tool results into a complete JSON response
5. Return ONLY a valid JSON object as your Final Answer
6. If extraction fails, return a JSON with empty/default values

Expected JSON structure:
{{
    "date": "YYYY-MM-DD or empty string",
    "weekday": "Monday/Tuesday/etc or empty string", 
    "day_number": 1,
    "location": "Location Name or empty string",
    "utc_offset": "+00:00",
    "notes": "Equipment and setup notes or empty string",
    "zodiacs": 0,
    "twins": 0,
    "activity_type": "Activity description or empty string",
    "groups": [],
    "schedule": [],
    "tides": []
}}

Use this format:

Question: {input}
Thought: I need to extract expedition information from this document text.
Action: extract_dates
Action Input: {input}
Observation: [tool result]
Thought: Now I'll extract location information.
Action: extract_locations  
Action Input: {input}
Observation: [tool result]
Thought: Let me get equipment information.
Action: extract_equipment
Action Input: {input}
Observation: [tool result]
Thought: I have all the information needed to create the final JSON response.
Final Answer: [VALID JSON ONLY]

Begin!

Question: {input}
Thought:{agent_scratchpad}"""

        tools = create_extraction_tools() or self._create_basic_tools()

        return PromptTemplate(
            template=template,
            input_variables=["input", "agent_scratchpad"],
            partial_variables={
                "tools": "\n".join([f"{tool.name}: {tool.description}" for tool in tools]),
                "tool_names": ", ".join([tool.name for tool in tools])
            }
        )
    
    def extract_entities(self, document_text: str) -> Dict[str, Any]:
        """
        Extract expedition entities from document text.
        
        Args:
            document_text: Text content from expedition document
            
        Returns:
            Dictionary containing extracted entities
        """
        # Input validation
        if not document_text or not document_text.strip():
            logger.warning("Empty document text provided")
            return self._get_empty_result()
        
        if not self.is_available():
            logger.error("Agent not available, falling back to regex extraction")
            return self._fallback_extraction(document_text)
        
        try:
            logger.info("Starting entity extraction with LangChain agent")

            # Validate input size
            if len(document_text.strip()) < 20:
                logger.warning("Document text too short for meaningful extraction")
                return self._fallback_extraction(document_text)

            # Prepare input with reasonable size limit
            text_limit = 4000  # Increased limit for better context
            processed_text = self._preprocess_text(document_text, text_limit)

            # Run agent with timeout using thread executor
            with ThreadPoolExecutor(max_workers=1) as executor:
                try:
                    future = executor.submit(self._run_agent, processed_text)
                    result = future.result(timeout=self.timeout)
                    
                    if result and "output" in result:
                        parsed_result = self._parse_agent_output(result["output"])
                        if self._validate_result(parsed_result):
                            logger.info("Agent extraction successful")
                            return parsed_result
                        else:
                            logger.warning("Agent output invalid, using fallback")
                            return self._fallback_extraction(document_text)
                    else:
                        logger.warning("No valid output from agent, using fallback")
                        return self._fallback_extraction(document_text)
                        
                except TimeoutError:
                    logger.error(f"Agent execution timed out after {self.timeout} seconds")
                    return self._fallback_extraction(document_text)

        except Exception as e:
            logger.error(f"Agent extraction failed: {e}")
            return self._fallback_extraction(document_text)
    
    def _preprocess_text(self, text: str, limit: int) -> str:
        """Preprocess text for better extraction."""
        # Clean up text
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Truncate if too long
        if len(text) > limit:
            # Try to truncate at sentence boundary
            truncated = text[:limit]
            last_period = truncated.rfind('.')
            if last_period > limit * 0.8:  # If period is in last 20%
                text = truncated[:last_period + 1]
            else:
                text = truncated
        
        return text
    
    def _run_agent(self, text: str) -> Dict[str, Any]:
        """Run the agent with the provided text and aggregate outputs."""
        response = self.agent_executor.invoke({"input": text})
        if response and "intermediate_steps" in response:
            steps = response["intermediate_steps"]
            outputs = [step[1] for step in steps if isinstance(step, tuple) and isinstance(step[1], dict)]
            aggregated = self._aggregate_tool_outputs(outputs)
            return {"output": aggregated}
        return {}
    
    def _validate_result(self, result: Dict[str, Any]) -> bool:
        """Validate that the result has meaningful content."""
        if not isinstance(result, dict):
            return False
        
        # Check if at least one meaningful field is populated
        meaningful_fields = ["location", "date", "zodiacs", "twins", "groups", "schedule", "tides"]
        for field in meaningful_fields:
            if field in result:
                value = result[field]
                if value and value != "" and value != 0 and value != []:
                    return True
        
        return False
    
    def _get_empty_result(self) -> Dict[str, Any]:
        """Return empty result structure."""
        return {
            "date": "",
            "weekday": "",
            "day_number": 1,
            "location": "",
            "utc_offset": "+00:00",
            "notes": "",
            "zodiacs": 0,
            "twins": 0,
            "activity_type": "",
            "groups": [],
            "schedule": [],
            "tides": []
        }
    
    def _aggregate_tool_outputs(self, outputs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Aggregate outputs from tools into the final structured result."""
        result = self._get_empty_result()
        for output in outputs:
            if isinstance(output, dict):
                result.update({k: v for k, v in output.items() if v})
        return result

    def _extract_from_text(self, text: str) -> Dict[str, Any]:
        """Extract key information from plain text output."""
        result = self._get_empty_result()

        # Extract basic information from text
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if 'location' in line.lower() and ':' in line:
                location = line.split(':', 1)[1].strip().strip('"\'')
                if location:
                    result["location"] = location
            elif 'date' in line.lower() and ':' in line:
                date = line.split(':', 1)[1].strip().strip('"\'')
                if date:
                    result["date"] = date
            elif 'zodiac' in line.lower() and any(char.isdigit() for char in line):
                numbers = re.findall(r'\d+', line)
                if numbers:
                    result["zodiacs"] = int(numbers[0])

        return result
    
    def _fallback_extraction(self, document_text: str) -> Dict[str, Any]:
        """
        Fallback extraction using regex patterns.

        Args:
            document_text: Document text to extract from

        Returns:
            Dictionary with extracted entities
        """
        logger.info("Using fallback regex extraction")

        extracted = self._get_empty_result()

        # Extract date patterns
        date_patterns = [
            r'Date[:\s]*(\d{4}-\d{2}-\d{2})',
            r'(\d{4}-\d{2}-\d{2})',
            r'Date[:\s]*(\d{1,2}/\d{1,2}/\d{4})',
            r'(\d{1,2}/\d{1,2}/\d{4})',
            r'Date[:\s]*(\d{1,2}-\d{1,2}-\d{4})',
            r'(\d{1,2}-\d{1,2}-\d{4})'
        ]

        for pattern in date_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                extracted["date"] = match.group(1)
                break

        # Extract weekday
        weekday_pattern = r'\b(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)\b'
        weekday_match = re.search(weekday_pattern, document_text, re.IGNORECASE)
        if weekday_match:
            extracted["weekday"] = weekday_match.group(1).title()

        # Extract location
        location_patterns = [
            r'Location[:\s]+([A-Za-z\s,.-]+?)(?:\n|$|Date|Day)',
            r'Site[:\s]+([A-Za-z\s,.-]+?)(?:\n|$|Date|Day)',
            r'Destination[:\s]+([A-Za-z\s,.-]+?)(?:\n|$|Date|Day)',
            r'at\s+([A-Z][A-Za-z\s,.-]+?)(?:\n|$|Date|Day)',
        ]

        for pattern in location_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                location = match.group(1).strip().strip(',.-')
                if 2 < len(location) < 100:
                    extracted["location"] = location
                    break

        # Extract zodiac counts
        zodiac_patterns = [
            r'(\d+)\s*zodiac',
            r'zodiac[s]?[:\s]*(\d+)',
            r'(\d+)\s*Zodiac'
        ]

        for pattern in zodiac_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                extracted["zodiacs"] = int(match.group(1))
                break

        # Extract twin counts
        twin_patterns = [
            r'(\d+)\s*twin',
            r'twin[s]?[:\s]*(\d+)',
            r'(\d+)\s*Twin'
        ]

        for pattern in twin_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                extracted["twins"] = int(match.group(1))
                break

        # Extract group information
        group_patterns = [
            r'(Yellow|Blue|Red|Green|Orange|Purple)\s*[Gg]roup[:\s]*(\d{1,2}:\d{2})',
            r'(\d{1,2}:\d{2})\s*-\s*(Yellow|Blue|Red|Green|Orange|Purple)\s*[Gg]roup'
        ]

        for pattern in group_patterns:
            matches = re.findall(pattern, document_text, re.IGNORECASE)
            for match in matches:
                if len(match) == 2:
                    if ':' in match[0]:  # Time first
                        time, color = match
                    else:  # Color first
                        color, time = match

                    extracted["groups"].append({
                        "groupName": color.title(),
                        "color": color.title(),
                        "departureTime": time,
                        "activity": "Unknown"
                    })

        # Extract schedule from times (if no groups found)
        if not extracted["groups"]:
            time_pattern = r'(\d{1,2}:\d{2})'
            times = re.findall(time_pattern, document_text)
            for time_str in times[:5]:
                extracted["schedule"].append({
                    "time": time_str,
                    "type": "event",
                    "description": f"Scheduled event at {time_str}",
                    "location": extracted["location"]
                })

        # Extract tide information
        tide_patterns = [
            r'(\d{1,2}:\d{2})\s*-\s*(?:Low|High)\s*Tide\s*\(([0-9.]+)m?\)',
            r'(?:Low|High)\s*Tide[:\s]*(\d{1,2}:\d{2})\s*[:\-\s]*([0-9.]+)m?'
        ]

        for pattern in tide_patterns:
            matches = re.findall(pattern, document_text, re.IGNORECASE)
            for match in matches:
                if len(match) == 2:
                    time, height = match
                    try:
                        height_float = float(height)
                        tide_type = "Low Tide" if "low" in document_text.lower() else "High Tide"
                        extracted["tides"].append({
                            "time": time,
                            "height": height_float,
                            "label": tide_type
                        })
                    except ValueError:
                        continue

        # Extract activity type
        activity_patterns = [
            r'Activity[:\s]+([A-Za-z\s]+)',
            r'Operation[:\s]+([A-Za-z\s]+)',
            r'(\d+h?\s*[A-Za-z\s]*(?:Cruise|Landing|Operation))'
        ]

        for pattern in activity_patterns:
            match = re.search(pattern, document_text, re.IGNORECASE)
            if match:
                activity = match.group(1).strip()
                if len(activity) > 2:
                    extracted["activity_type"] = activity
                    break

        # Extract notes from equipment section
        equipment_section = re.search(r'Equipment[:\s]*([^.]*(?:\.[^.]*)*)', document_text, re.IGNORECASE | re.DOTALL)
        if equipment_section:
            notes = equipment_section.group(1).strip()
            notes = re.sub(r'\s+', ' ', notes)
            if len(notes) > 10:
                extracted["notes"] = notes[:200]

        logger.info(f"Fallback extraction completed for location: '{extracted['location']}', date: '{extracted['date']}'")
        return extracted
    
    def is_available(self) -> bool:
        """Check if extraction agent is ready to use."""
        return LANGCHAIN_AVAILABLE and self.agent_executor is not None
    
    def health_check(self) -> Dict[str, Any]:
        """Perform a health check on the agent."""
        status = {
            "langchain_available": LANGCHAIN_AVAILABLE,
            "llm_initialized": self.llm is not None,
            "agent_initialized": self.agent_executor is not None,
            "ready": self.is_available()
        }
        
        if self.llm:
            try:
                test_response = self.llm.invoke("Hello")
                status["llm_responsive"] = True
            except Exception as e:
                status["llm_responsive"] = False
                status["llm_error"] = str(e)
        
        return status
