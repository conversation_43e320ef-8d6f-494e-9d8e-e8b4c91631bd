#!/usr/bin/env python3
"""
Demo script for Expo - creates sample documents and processes them.
"""

import tempfile
import json
from pathlib import Path
import sys

# Add Expo to path
sys.path.insert(0, str(Path(__file__).parent))

from main import ExpoApp


def create_sample_documents():
    """Create sample expedition documents for testing."""
    documents = []
    
    # Sample 1: Simple expedition document
    doc1_content = """
    EXPEDITION PLANNING DOCUMENT
    
    Location: The Lacepedes Islands
    Date: 2024-07-12
    Day: Friday
    Operation: Day 2
    
    EQUIPMENT REQUIREMENTS:
    - 8 Zodiac boats
    - 1 Twin boat
    - Safety equipment
    - Landing gear
    
    SCHEDULE:
    07:40 - Yellow Group departure (30 passengers)
    09:40 - Blue Group departure (30 passengers)
    11:40 - Red Group departure (30 passengers)
    13:40 - Green Group departure (30 passengers)
    
    TIDE INFORMATION:
    02:00 - Low Tide (2.0m)
    08:15 - Rising
    14:30 - High Tide (4.5m)
    20:45 - Falling
    
    ACTIVITY: 2-hour Zodiac Cruise
    
    NOTES:
    - Beach setup required
    - Safety briefing mandatory
    - Weather conditions favorable
    """
    
    # Sample 2: Different format
    doc2_content = """
    DAILY OPERATIONS BRIEF
    
    Site: Paradise Bay
    Date: 2024-07-13
    Weekday: Saturday
    
    Logistics:
    * 6 Zodiacs available
    * 2 Twin boats
    * All-day operation
    
    Group Schedule:
    - Yellow: 08:00 departure, 18:00 return
    - Blue: 08:30 departure, 18:30 return
    
    Tides:
    Low: 03:30 (1.8m)
    High: 15:45 (4.2m)
    
    Activity Type: Full Day Landing
    """
    
    # Sample 3: Minimal format
    doc3_content = """
    Location: Deception Island
    Date: 2024-07-14
    Sunday
    
    4 zodiac + 1 twin
    Morning operation only
    
    Groups:
    Red 09:00
    Green 10:30
    
    Low tide 04:15 - 1.5m
    """
    
    # Create temporary files
    for i, content in enumerate([doc1_content, doc2_content, doc3_content], 1):
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(content)
            documents.append(f.name)
            print(f"📄 Created sample document {i}: {Path(f.name).name}")
    
    return documents


def run_demo():
    """Run the Expo demo."""
    print("🚀 Expo Demo - Expedition Document Processor")
    print("=" * 60)
    
    try:
        # Create sample documents
        print("\n📝 Creating sample expedition documents...")
        sample_docs = create_sample_documents()
        
        # Initialize Expo
        print("\n🔧 Initializing Expo...")
        app = ExpoApp()
        
        # Check component availability
        print(f"📄 Document processor available: {app.document_processor.is_available()}")
        print(f"🤖 Extraction agent available: {app.extraction_agent.is_available()}")
        
        # Process documents
        print("\n⚙️  Processing documents...")
        results = []
        
        for i, doc_path in enumerate(sample_docs, 1):
            print(f"\n--- Processing Document {i} ---")
            print(f"File: {Path(doc_path).name}")
            
            try:
                # Process single document
                result = app.process_single_file(doc_path)
                
                if result:
                    results.append(result)
                    print(f"✅ Generated: {Path(result).name}")
                    
                    # Show extracted data preview
                    with open(result, 'r') as f:
                        data = json.load(f)
                    
                    print(f"   Location: {data.get('location', 'Unknown')}")
                    print(f"   Date: {data.get('date', 'Unknown')}")
                    print(f"   Zodiacs: {data.get('zodiacs', 0)}")
                    print(f"   Groups: {len(data.get('groups', []))}")
                    
                else:
                    print("❌ Processing failed")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
        
        # Summary
        print("\n" + "=" * 60)
        print("📊 DEMO SUMMARY")
        print("=" * 60)
        print(f"Documents processed: {len(sample_docs)}")
        print(f"JSON files generated: {len(results)}")
        
        if results:
            print(f"\nOutput files:")
            for result in results:
                print(f"  • {Path(result).name}")
            
            print(f"\nOutput directory: {app.json_generator.template_schema}")
            print("\n🎉 Demo completed successfully!")
            
            # Show first result in detail
            if results:
                print(f"\n📋 Sample Output (first file):")
                print("-" * 40)
                with open(results[0], 'r') as f:
                    sample_data = json.load(f)
                
                print(json.dumps(sample_data, indent=2)[:500] + "...")
        
        else:
            print("\n❌ No files were processed successfully")
        
        return len(results) > 0
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        return False
        
    finally:
        # Clean up sample documents
        print("\n🧹 Cleaning up sample documents...")
        for doc_path in sample_docs:
            try:
                Path(doc_path).unlink()
                print(f"   Removed: {Path(doc_path).name}")
            except:
                pass


def main():
    """Main demo entry point."""
    success = run_demo()
    
    if success:
        print("\n✨ Ready to use Expo with your own documents!")
        print("\nUsage:")
        print("  GUI: python -m Expo.main")
        print("  CLI: python -m Expo.cli your_document.pdf")
        return 0
    else:
        print("\n❌ Demo failed. Please check the setup.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
