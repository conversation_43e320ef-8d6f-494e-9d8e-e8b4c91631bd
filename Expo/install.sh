#!/bin/bash

# Expo Installation Script
# Installs Expo as a standalone expedition document processor

set -e

echo "🚀 Installing Expo - Expedition Document Processor"
echo "=================================================="

# Check Python version
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.9"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Error: Python 3.9+ required. Found: $python_version"
    exit 1
fi

echo "✅ Python version: $python_version"

# Check if Ollama is installed
if ! command -v ollama &> /dev/null; then
    echo "⚠️  Warning: Ollama not found. Please install Ollama for LLM functionality."
    echo "   Visit: https://ollama.ai"
else
    echo "✅ Ollama found"
    
    # Check if preferred model is available
    if ollama list | grep -q "mistral-7b-v0-1-gguf:latest"; then
        echo "✅ Preferred model (mistral-7b-v0-1-gguf:latest) found"
    else
        echo "📥 Installing preferred model..."
        ollama pull mistral-7b-v0-1-gguf:latest
    fi
fi

# Create virtual environment
echo "📦 Creating virtual environment..."
python3 -m venv expo_env
source expo_env/bin/activate

# Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📚 Installing dependencies..."
pip install -r requirements.txt

# Install Expo in development mode
echo "🔧 Installing Expo..."
pip install -e .

# Create output directory
echo "📁 Creating output directory..."
mkdir -p ~/Desktop/output

# Test installation
echo "🧪 Testing installation..."
python -c "
import Expo
from Expo.core.document_processor import DocumentProcessor
from Expo.agents.extraction_agent import ExtractionAgent
print('✅ Expo modules imported successfully')

# Test document processor
processor = DocumentProcessor()
print(f'✅ Document processor available: {processor.is_available()}')

# Test extraction agent
agent = ExtractionAgent()
print(f'✅ Extraction agent available: {agent.is_available()}')
"

echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "Usage:"
echo "  # Activate environment"
echo "  source expo_env/bin/activate"
echo ""
echo "  # GUI interface"
echo "  python -m Expo.main"
echo ""
echo "  # CLI interface"
echo "  python -m Expo.cli document.pdf"
echo ""
echo "  # Or use installed commands"
echo "  expo-gui"
echo "  expo document.pdf"
echo ""
echo "Output directory: ~/Desktop/output"
echo ""
