"""
Output management for Expo - handles saving and organizing JSON outputs.
"""

import json
import logging
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
import re

from ..config.settings import settings

logger = logging.getLogger(__name__)


class OutputManager:
    """
    Manages output files and directory organization for Expo.
    """
    
    def __init__(self, output_directory: Optional[str] = None):
        """
        Initialize output manager.
        
        Args:
            output_directory: Optional override for output directory
        """
        self.output_directory = Path(output_directory) if output_directory else settings.output_path
        self.ensure_output_directory()
        
        # Track processed files
        self.processed_files = []
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        logger.info(f"Output manager initialized - Directory: {self.output_directory}")
    
    def ensure_output_directory(self):
        """Ensure output directory exists."""
        try:
            self.output_directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"Output directory ready: {self.output_directory}")
        except Exception as e:
            logger.error(f"Failed to create output directory: {e}")
            raise
    
    def save_json(self, data: Dict[str, Any], source_file: str = None, custom_name: str = None) -> str:
        """
        Save JSON data to output directory with proper naming.
        
        Args:
            data: JSON data to save
            source_file: Original source file path (for naming)
            custom_name: Custom filename (without extension)
            
        Returns:
            Path to saved JSON file
        """
        try:
            # Generate filename
            if custom_name:
                filename = f"{custom_name}.json"
            else:
                filename = self._generate_filename(data, source_file)
            
            output_path = self.output_directory / filename
            
            # Handle file conflicts
            output_path = self._resolve_filename_conflict(output_path)
            
            # Save JSON with proper formatting
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=settings.json_indent, ensure_ascii=False)
            
            # Track processed file
            self.processed_files.append({
                "source": source_file,
                "output": str(output_path),
                "timestamp": datetime.now().isoformat(),
                "location": data.get("location", "Unknown"),
                "date": data.get("date", "Unknown")
            })
            
            logger.info(f"Saved JSON: {output_path.name}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Failed to save JSON: {e}")
            raise
    
    def _generate_filename(self, data: Dict[str, Any], source_file: str = None) -> str:
        """
        Generate filename for JSON output using location-date format.
        
        Args:
            data: JSON data containing location and date
            source_file: Original source file path
            
        Returns:
            Generated filename
        """
        # Extract location and date from data
        location = data.get("location", "unknown")
        date = data.get("date", "")
        
        # Clean location name for filename
        location_clean = self._clean_filename_part(location)
        
        # Use date if available, otherwise extract from source file or use timestamp
        if date:
            date_clean = date
        elif source_file:
            # Try to extract date from source filename
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', Path(source_file).name)
            if date_match:
                date_clean = date_match.group(1)
            else:
                date_clean = datetime.now().strftime("%Y-%m-%d")
        else:
            date_clean = datetime.now().strftime("%Y-%m-%d")
        
        # Generate filename: location-date.json
        filename = f"{location_clean}-{date_clean}.json"
        
        return filename
    
    def _clean_filename_part(self, text: str) -> str:
        """Clean text for use in filename."""
        # Remove special characters and normalize
        clean = re.sub(r'[^\w\s-]', '', text.lower())
        clean = re.sub(r'[-\s]+', '-', clean).strip('-')
        
        # Limit length
        if len(clean) > 30:
            clean = clean[:30].rstrip('-')
        
        return clean or "unknown"
    
    def _resolve_filename_conflict(self, output_path: Path) -> Path:
        """
        Resolve filename conflicts by adding counter.
        
        Args:
            output_path: Desired output path
            
        Returns:
            Available output path
        """
        if not output_path.exists():
            return output_path
        
        # Add counter to filename
        counter = 1
        stem = output_path.stem
        suffix = output_path.suffix
        
        while output_path.exists():
            new_name = f"{stem}-{counter}{suffix}"
            output_path = output_path.parent / new_name
            counter += 1
        
        return output_path
    
    def create_session_summary(self) -> str:
        """
        Create a summary file for the current processing session.
        
        Returns:
            Path to summary file
        """
        try:
            summary_data = {
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat(),
                "total_files": len(self.processed_files),
                "output_directory": str(self.output_directory),
                "processed_files": self.processed_files
            }
            
            summary_filename = f"session_summary_{self.session_id}.json"
            summary_path = self.output_directory / summary_filename
            
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Created session summary: {summary_path.name}")
            return str(summary_path)
            
        except Exception as e:
            logger.error(f"Failed to create session summary: {e}")
            return None
    
    def get_output_files(self) -> List[str]:
        """Get list of output files from current session."""
        return [item["output"] for item in self.processed_files]
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get statistics for current session."""
        return {
            "session_id": self.session_id,
            "total_files": len(self.processed_files),
            "output_directory": str(self.output_directory),
            "locations": list(set(item["location"] for item in self.processed_files)),
            "dates": list(set(item["date"] for item in self.processed_files))
        }
    
    def cleanup_old_files(self, days_old: int = 30):
        """
        Clean up old output files.
        
        Args:
            days_old: Remove files older than this many days
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
            removed_count = 0
            
            for file_path in self.output_directory.glob("*.json"):
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    removed_count += 1
            
            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} old files")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def backup_outputs(self, backup_directory: str) -> str:
        """
        Create backup of output directory.
        
        Args:
            backup_directory: Directory to create backup in
            
        Returns:
            Path to backup directory
        """
        try:
            backup_path = Path(backup_directory) / f"expo_backup_{self.session_id}"
            backup_path.mkdir(parents=True, exist_ok=True)
            
            # Copy all JSON files
            for json_file in self.output_directory.glob("*.json"):
                shutil.copy2(json_file, backup_path)
            
            logger.info(f"Created backup: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            raise
