"""
JSON template generator for Expo.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
import re

from config.settings import settings

logger = logging.getLogger(__name__)


class JSONGenerator:
    """
    Generates structured JSON templates from extracted expedition data.
    """
    
    def __init__(self):
        """Initialize the JSON generator."""
        self.template_schema = self._get_template_schema()
    
    def _get_template_schema(self) -> Dict[str, Any]:
        """Get the expedition JSON template schema."""
        return {
            "dayNumber": 1,
            "weekday": "",
            "date": "",
            "location": "",
            "utcOffset": "+00:00",
            "notes": "",
            "zodiacs": 0,
            "twins": 0,
            "activityType": "",
            "groups": [],
            "schedule": [],
            "tides": [],
            "groupOrder": ["Yellow", "Blue", "Red", "Green"],
            "zodiacDrivers": [],
            "landingGuides": {
                "wave1Guides": [],
                "wave2Guides": []
            },
            "nextDayLocation": "",
            "sunTimes": {
                "twilightStart": "",
                "sunrise": "",
                "sunset": "",
                "twilightEnd": ""
            }
        }
    
    def create_template(self, extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a JSON template from extracted data.
        
        Args:
            extracted_data: Dictionary containing extracted entities
            
        Returns:
            Structured JSON template
        """
        logger.info("Creating JSON template from extracted data")
        
        # Start with base template
        template = self.template_schema.copy()
        
        # Populate template with extracted data
        template.update({
            "dayNumber": extracted_data.get("day_number", 1),
            "weekday": extracted_data.get("weekday", ""),
            "date": extracted_data.get("date", ""),
            "location": extracted_data.get("location", "Unknown Location"),
            "utcOffset": extracted_data.get("utc_offset", "+00:00"),
            "notes": extracted_data.get("notes", ""),
            "zodiacs": extracted_data.get("zodiacs", 0),
            "twins": extracted_data.get("twins", 0),
            "activityType": extracted_data.get("activity_type", ""),
            "groups": extracted_data.get("groups", []),
            "schedule": extracted_data.get("schedule", []),
            "tides": extracted_data.get("tides", []),
            "nextDayLocation": extracted_data.get("next_location", ""),
        })
        
        # Update group order if groups are present
        if template["groups"]:
            group_colors = [group.get("color", "") for group in template["groups"] if group.get("color")]
            if group_colors:
                template["groupOrder"] = group_colors
        
        # Add zodiac drivers if present
        if "zodiac_drivers" in extracted_data:
            template["zodiacDrivers"] = extracted_data["zodiac_drivers"]
        
        # Add landing guides if present
        if "landing_guides" in extracted_data:
            template["landingGuides"] = extracted_data["landing_guides"]
        
        # Add sun times if present
        if "sun_times" in extracted_data:
            template["sunTimes"] = extracted_data["sun_times"]
        
        logger.info(f"Created template for location: {template['location']}, date: {template['date']}")
        return template
    
    def save_output(self, json_data: Dict[str, Any], source_file: str = None) -> str:
        """
        Save JSON template to output directory.
        
        Args:
            json_data: JSON template data
            source_file: Original source file path (for naming)
            
        Returns:
            Path to saved JSON file
        """
        # Generate filename
        filename = self._generate_filename(json_data, source_file)
        output_path = settings.output_path / filename
        
        # Ensure output directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # Save JSON with proper formatting
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=settings.json_indent, ensure_ascii=False)
            
            logger.info(f"Saved JSON template to: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Failed to save JSON template: {e}")
            raise
    
    def _generate_filename(self, json_data: Dict[str, Any], source_file: str = None) -> str:
        """
        Generate filename for JSON output.
        
        Args:
            json_data: JSON template data
            source_file: Original source file path
            
        Returns:
            Generated filename
        """
        # Extract location and date
        location = json_data.get("location", "unknown")
        date = json_data.get("date", "")
        
        # Clean location name for filename
        location_clean = re.sub(r'[^\w\s-]', '', location.lower())
        location_clean = re.sub(r'[-\s]+', '-', location_clean).strip('-')
        
        # Use date if available, otherwise use timestamp
        if date:
            date_clean = date
        else:
            date_clean = datetime.now().strftime("%Y-%m-%d")
        
        # Generate filename: location-date.json
        filename = f"{location_clean}-{date_clean}.json"
        
        # If file exists, add counter
        counter = 1
        original_filename = filename
        while (settings.output_path / filename).exists():
            name_part = original_filename.replace('.json', '')
            filename = f"{name_part}-{counter}.json"
            counter += 1
        
        return filename
    
    def validate_template(self, json_data: Dict[str, Any]) -> List[str]:
        """
        Validate JSON template structure.
        
        Args:
            json_data: JSON template to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check required fields
        required_fields = ["dayNumber", "weekday", "date", "location"]
        for field in required_fields:
            if field not in json_data or not json_data[field]:
                errors.append(f"Missing required field: {field}")
        
        # Validate data types
        if "dayNumber" in json_data and not isinstance(json_data["dayNumber"], int):
            errors.append("dayNumber must be an integer")
        
        if "zodiacs" in json_data and not isinstance(json_data["zodiacs"], int):
            errors.append("zodiacs must be an integer")
        
        if "twins" in json_data and not isinstance(json_data["twins"], int):
            errors.append("twins must be an integer")
        
        if "groups" in json_data and not isinstance(json_data["groups"], list):
            errors.append("groups must be a list")
        
        if "schedule" in json_data and not isinstance(json_data["schedule"], list):
            errors.append("schedule must be a list")
        
        if "tides" in json_data and not isinstance(json_data["tides"], list):
            errors.append("tides must be a list")
        
        # Validate date format
        if "date" in json_data and json_data["date"]:
            try:
                datetime.strptime(json_data["date"], "%Y-%m-%d")
            except ValueError:
                errors.append("date must be in YYYY-MM-DD format")
        
        return errors
    
    def get_template_info(self) -> Dict[str, Any]:
        """Get information about the template schema."""
        return {
            "schema": self.template_schema,
            "required_fields": ["dayNumber", "weekday", "date", "location"],
            "output_directory": str(settings.output_path),
            "naming_convention": "location-date.json"
        }
