"""
JSON template generator for Expo using new structured schema.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
import re

from config.settings import settings

logger = logging.getLogger(__name__)


class JSONGenerator:
    """
    Generates structured JSON templates from extracted expedition data.
    """
    
    def __init__(self):
        """Initialize the JSON generator."""
        self.template_schema = self._get_template_schema()
    
    def _get_template_schema(self) -> Dict[str, Any]:
        """Get the new structured expedition JSON template schema."""
        return {
            "dayNumber": 1,
            "weekday": "",
            "date": "",
            "location": "",
            "utcOffset": "+00:00",
            "notes": {
                "general": "",
                "setupNotes": []
            },
            "arrival": {
                "time": "",
                "note": ""
            },
            "departure": {
                "time": "",
                "note": ""
            },
            "tides": [],
            "activities": [],
            "schedule": [],
            "groupOrder": ["Yellow", "Blue", "Red", "Green"],
            "nextDayLocation": "",
            "sunTimes": {
                "twilightStart": "",
                "sunrise": "",
                "sunset": "",
                "twilightEnd": ""
            },
            "metadata": {
                "createdBy": "ExpoApp",
                "generatedFrom": "",
                "confidenceScore": 0.0
            }
        }
    
    def create_template(self, extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a JSON template from extracted data.

        Args:
            extracted_data: Dictionary containing extracted entities

        Returns:
            Structured JSON template
        """
        logger.info("Creating JSON template from extracted data")

        # Validate minimum required fields
        if not self._validate_minimum_data(extracted_data):
            logger.warning("Extracted data does not meet minimum requirements")
            return None

        # Start with base template
        template = self.template_schema.copy()

        # Basic information
        template.update({
            "dayNumber": extracted_data.get("day_number", 1),
            "weekday": extracted_data.get("weekday", ""),
            "date": extracted_data.get("date", ""),
            "location": self._clean_text(extracted_data.get("location", "Unknown Location")),
            "utcOffset": extracted_data.get("utc_offset", "+00:00"),
            "nextDayLocation": self._clean_text(extracted_data.get("next_location", "")),
        })

        # Notes structure
        notes_text = self._clean_text(extracted_data.get("notes", ""))
        if notes_text:
            template["notes"]["general"] = notes_text
            # Try to extract setup notes from general notes
            setup_notes = self._extract_setup_notes(notes_text)
            if setup_notes:
                template["notes"]["setupNotes"] = setup_notes

        # Arrival/Departure times
        schedule_events = extracted_data.get("schedule", [])
        groups = extracted_data.get("groups", [])

        # Find earliest and latest times for arrival/departure
        if schedule_events or groups:
            times = []
            if schedule_events:
                times.extend([event.get("time", "") for event in schedule_events if event.get("time")])
            if groups:
                times.extend([group.get("departureTime", "") for group in groups if group.get("departureTime")])
                times.extend([group.get("returnTime", "") for group in groups if group.get("returnTime")])

            if times:
                times = [t for t in times if t and ":" in t]  # Valid time format
                if times:
                    template["arrival"]["time"] = min(times)
                    template["arrival"]["note"] = f"Scheduled arrival at {template['location']}"
                    template["departure"]["time"] = max(times)
                    template["departure"]["note"] = "Ship departs"

        # Tides - convert to new format
        tides = extracted_data.get("tides", [])
        formatted_tides = []
        for tide in tides:
            if isinstance(tide, dict) and "time" in tide and "height" in tide:
                tide_type = "low" if "low" in tide.get("label", "").lower() else "high"
                formatted_tides.append({
                    "time": tide["time"],
                    "height": tide["height"],
                    "type": tide_type
                })
        template["tides"] = formatted_tides

        # Activities - create Zodiac Cruise activity if we have equipment data
        zodiacs = extracted_data.get("zodiacs", 0)
        twins = extracted_data.get("twins", 0)
        activity_type = extracted_data.get("activity_type", "")

        if zodiacs > 0 or groups:
            activity = {
                "type": "Zodiac Cruise",
                "dropTime": template["arrival"]["time"] or "08:00",
                "duration": self._extract_duration(activity_type) or "2h",
                "zodiacCount": zodiacs,
                "twinsCount": twins,
                "groups": self._format_groups_for_activity(groups),
                "drivers": [],  # Would need more data to populate
                "guides": None,
                "notes": None
            }
            template["activities"] = [activity]

        # Schedule - create structured schedule events
        template["schedule"] = self._create_schedule_events(
            extracted_data, template["arrival"]["time"], template["departure"]["time"]
        )

        # Update group order if groups are present
        if groups:
            group_colors = [group.get("color", "") for group in groups if group.get("color")]
            if group_colors:
                template["groupOrder"] = group_colors

        # Metadata
        template["metadata"]["generatedFrom"] = extracted_data.get("source_file", "")
        template["metadata"]["confidenceScore"] = self._calculate_confidence_score(extracted_data)

        logger.info(f"Created template for location: {template['location']}, date: {template['date']}")
        return template

    def _validate_minimum_data(self, extracted_data: Dict[str, Any]) -> bool:
        """
        Validate that extracted data meets minimum requirements.

        Args:
            extracted_data: Dictionary containing extracted entities

        Returns:
            True if data meets minimum requirements
        """
        # Check for minimum required fields
        location = extracted_data.get("location", "").strip()
        date = extracted_data.get("date", "").strip()

        # Must have at least a location
        if not location or location.lower() in ["unknown", "unknown location", ""]:
            logger.warning("No valid location found in extracted data")
            return False

        # Should have a date, but not strictly required
        if not date:
            logger.warning("No date found in extracted data")

        return True

    def _clean_text(self, text: str) -> str:
        """
        Clean text by removing control characters and normalizing whitespace.

        Args:
            text: Text to clean

        Returns:
            Cleaned text
        """
        if not text:
            return ""

        # Remove control characters and normalize whitespace
        import re

        # Remove control characters (except tab and newline)
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', str(text))

        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)

        # Strip leading/trailing whitespace
        text = text.strip()

        return text

    def _extract_setup_notes(self, notes_text: str) -> List[str]:
        """Extract setup notes from general notes text."""
        setup_notes = []

        # Look for bullet points or list items
        lines = notes_text.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('•') or line.startswith('-') or line.startswith('*'):
                note = line[1:].strip()
                if note and len(note) > 3:
                    setup_notes.append(note)

        return setup_notes

    def _extract_duration(self, activity_type: str) -> str:
        """Extract duration from activity type text."""
        if not activity_type:
            return "2h"

        # Look for duration patterns
        duration_patterns = [
            r'(\d+h)',
            r'(\d+\s*hour)',
            r'(\d+\s*hr)'
        ]

        for pattern in duration_patterns:
            match = re.search(pattern, activity_type, re.IGNORECASE)
            if match:
                return match.group(1).replace(' ', '')

        return "2h"  # Default

    def _format_groups_for_activity(self, groups: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format groups for the new activity structure."""
        formatted_groups = []

        for group in groups:
            if isinstance(group, dict):
                formatted_group = {
                    "groupName": group.get("groupName", ""),
                    "color": group.get("color", ""),
                    "startTime": group.get("departureTime", ""),
                    "returnTime": group.get("returnTime", "")
                }
                formatted_groups.append(formatted_group)

        return formatted_groups

    def _create_schedule_events(self, extracted_data: Dict[str, Any], arrival_time: str, departure_time: str) -> List[Dict[str, Any]]:
        """Create structured schedule events."""
        events = []

        # Add arrival event
        if arrival_time:
            events.append({
                "time": arrival_time,
                "type": "arrival",
                "description": f"Scheduled arrival at {extracted_data.get('location', 'location')}"
            })

        # Add zodiac drop event if we have equipment
        zodiacs = extracted_data.get("zodiacs", 0)
        twins = extracted_data.get("twins", 0)
        if zodiacs > 0 and arrival_time:
            zodiac_desc = f"Drop {zodiacs} Zodiacs"
            if twins > 0:
                zodiac_desc += f" + {twins} Twin"

            events.append({
                "time": arrival_time,
                "type": "drop_zodiacs",
                "description": zodiac_desc,
                "activityRef": "Zodiac Cruise"
            })

        # Add group events
        groups = extracted_data.get("groups", [])
        for group in groups:
            if isinstance(group, dict):
                group_name = group.get("groupName", "")
                start_time = group.get("departureTime", "")
                return_time = group.get("returnTime", "")

                if start_time:
                    events.append({
                        "time": start_time,
                        "type": "disembark",
                        "description": f"{group_name} group disembark for Zodiac Cruise",
                        "group": group_name,
                        "activityRef": "Zodiac Cruise"
                    })

                if return_time:
                    events.append({
                        "time": return_time,
                        "type": "return",
                        "description": f"{group_name} group returns",
                        "group": group_name,
                        "activityRef": "Zodiac Cruise"
                    })

        # Add departure event
        if departure_time:
            events.append({
                "time": departure_time,
                "type": "ship_depart",
                "description": "Ship departs"
            })

        # Sort events by time
        events.sort(key=lambda x: x.get("time", ""))

        return events

    def _calculate_confidence_score(self, extracted_data: Dict[str, Any]) -> float:
        """Calculate confidence score based on extracted data quality."""
        score = 0.0
        total_checks = 0

        # Check for required fields
        required_fields = ["location", "date"]
        for field in required_fields:
            total_checks += 1
            if extracted_data.get(field):
                score += 0.3

        # Check for optional but important fields
        optional_fields = ["weekday", "zodiacs", "twins", "groups", "tides"]
        for field in optional_fields:
            total_checks += 1
            if extracted_data.get(field):
                score += 0.1

        # Normalize score
        if total_checks > 0:
            score = min(score, 1.0)

        return round(score, 2)
    
    def save_output(self, json_data: Dict[str, Any], source_file: str = None) -> str:
        """
        Save JSON template to output directory.
        
        Args:
            json_data: JSON template data
            source_file: Original source file path (for naming)
            
        Returns:
            Path to saved JSON file
        """
        # Generate filename
        filename = self._generate_filename(json_data, source_file)
        output_path = settings.output_path / filename
        
        # Ensure output directory exists
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # Save JSON with proper formatting
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=settings.json_indent, ensure_ascii=False)
            
            logger.info(f"Saved JSON template to: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Failed to save JSON template: {e}")
            raise
    
    def _generate_filename(self, json_data: Dict[str, Any], source_file: str = None) -> str:
        """
        Generate filename for JSON output.
        
        Args:
            json_data: JSON template data
            source_file: Original source file path
            
        Returns:
            Generated filename
        """
        # Extract location and date
        location = json_data.get("location", "unknown")
        date = json_data.get("date", "")
        
        # Clean location name for filename
        location_clean = re.sub(r'[^\w\s-]', '', location.lower())
        location_clean = re.sub(r'[-\s]+', '-', location_clean).strip('-')
        
        # Use date if available, otherwise use timestamp
        if date:
            date_clean = date
        else:
            date_clean = datetime.now().strftime("%Y-%m-%d")
        
        # Generate filename: location-date.json
        filename = f"{location_clean}-{date_clean}.json"
        
        # If file exists, add counter
        counter = 1
        original_filename = filename
        while (settings.output_path / filename).exists():
            name_part = original_filename.replace('.json', '')
            filename = f"{name_part}-{counter}.json"
            counter += 1
        
        return filename
    
    def validate_template(self, json_data: Dict[str, Any]) -> List[str]:
        """
        Validate JSON template structure.
        
        Args:
            json_data: JSON template to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check required fields
        required_fields = ["dayNumber", "weekday", "date", "location"]
        for field in required_fields:
            if field not in json_data or not json_data[field]:
                errors.append(f"Missing required field: {field}")
        
        # Validate data types
        if "dayNumber" in json_data and not isinstance(json_data["dayNumber"], int):
            errors.append("dayNumber must be an integer")
        
        if "zodiacs" in json_data and not isinstance(json_data["zodiacs"], int):
            errors.append("zodiacs must be an integer")
        
        if "twins" in json_data and not isinstance(json_data["twins"], int):
            errors.append("twins must be an integer")
        
        if "groups" in json_data and not isinstance(json_data["groups"], list):
            errors.append("groups must be a list")
        
        if "schedule" in json_data and not isinstance(json_data["schedule"], list):
            errors.append("schedule must be a list")
        
        if "tides" in json_data and not isinstance(json_data["tides"], list):
            errors.append("tides must be a list")
        
        # Validate date format
        if "date" in json_data and json_data["date"]:
            try:
                datetime.strptime(json_data["date"], "%Y-%m-%d")
            except ValueError:
                errors.append("date must be in YYYY-MM-DD format")
        
        return errors
    
    def get_template_info(self) -> Dict[str, Any]:
        """Get information about the template schema."""
        return {
            "schema": self.template_schema,
            "required_fields": ["dayNumber", "weekday", "date", "location"],
            "output_directory": str(settings.output_path),
            "naming_convention": "location-date.json"
        }
