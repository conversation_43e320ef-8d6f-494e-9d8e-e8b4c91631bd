"""
Document processor for Expo using Docling for text extraction.
"""

import logging
from pathlib import Path
from typing import Optional, Dict, Any
import mimetypes

# Import docling for document processing
try:
    from docling.document_converter import DocumentConverter
    from docling.datamodel.base_models import InputFormat
    from docling.datamodel.pipeline_options import PdfPipelineOptions
    from docling.document_converter import PdfFormatOption
    DOCLING_AVAILABLE = True
except ImportError:
    DOCLING_AVAILABLE = False
    logging.warning("Docling not available. Install with: pip install docling")

from config.settings import settings

logger = logging.getLogger(__name__)


class DocumentProcessor:
    """
    Standalone document processor using Docling for text extraction.
    
    Supports: PDF, DOCX, DOC, TXT files
    """
    
    def __init__(self):
        """Initialize the document processor."""
        self.converter = None
        self._setup_docling()
    
    def _setup_docling(self):
        """Setup Docling converter with optimized settings."""
        if not DOCLING_AVAILABLE:
            logger.error("Docling is not available. Please install it.")
            return
        
        try:
            # Configure PDF processing options
            pdf_options = PdfPipelineOptions()
            pdf_options.do_ocr = settings.enable_ocr
            pdf_options.do_table_structure = True
            pdf_options.table_structure_options.do_cell_matching = True
            
            # Create converter with PDF options
            format_options = {
                InputFormat.PDF: PdfFormatOption(pipeline_options=pdf_options)
            }
            
            self.converter = DocumentConverter(format_options=format_options)
            logger.info("Docling converter initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Docling converter: {e}")
            self.converter = None
    
    def extract_text(self, file_path: str) -> Optional[str]:
        """
        Extract text from a document file.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Extracted text content or None if extraction fails
        """
        file_path_obj = Path(file_path)
        
        # Validate file
        if not file_path_obj.exists():
            logger.error(f"File not found: {file_path}")
            return None
        
        if not settings.is_supported_file(file_path):
            logger.error(f"Unsupported file type: {file_path_obj.suffix}")
            return None
        
        # Check file size
        file_size_mb = file_path_obj.stat().st_size / (1024 * 1024)
        if file_size_mb > settings.max_file_size_mb:
            logger.error(f"File too large: {file_size_mb:.1f}MB > {settings.max_file_size_mb}MB")
            return None
        
        logger.info(f"Processing document: {file_path}")
        
        try:
            # Handle different file types
            if file_path_obj.suffix.lower() == '.txt':
                return self._extract_text_file(file_path_obj)
            else:
                return self._extract_with_docling(file_path_obj)
                
        except Exception as e:
            logger.error(f"Error extracting text from {file_path}: {e}")
            return None
    
    def _extract_text_file(self, file_path: Path) -> str:
        """Extract text from plain text file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"Successfully extracted text from: {file_path}")
            return content
        except UnicodeDecodeError:
            # Try with different encoding
            with open(file_path, 'r', encoding='latin-1') as f:
                content = f.read()
            logger.info(f"Successfully extracted text with latin-1 encoding: {file_path}")
            return content
    
    def _extract_with_docling(self, file_path: Path) -> Optional[str]:
        """Extract text using Docling."""
        if not self.converter:
            logger.error("Docling converter not available")
            return None
        
        try:
            # Convert document
            result = self.converter.convert(str(file_path))
            
            # Extract markdown content
            if result and result.document:
                content = result.document.export_to_markdown()
                logger.info(f"Successfully extracted text using Docling: {file_path}")
                return content
            else:
                logger.warning(f"No content extracted from: {file_path}")
                return None
                
        except Exception as e:
            logger.error(f"Docling extraction failed for {file_path}: {e}")
            return None
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        Get metadata information about a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary with file metadata
        """
        file_path_obj = Path(file_path)
        
        if not file_path_obj.exists():
            return {"error": "File not found"}
        
        stat = file_path_obj.stat()
        mime_type, _ = mimetypes.guess_type(str(file_path_obj))
        
        return {
            "name": file_path_obj.name,
            "path": str(file_path_obj),
            "size_bytes": stat.st_size,
            "size_mb": round(stat.st_size / (1024 * 1024), 2),
            "extension": file_path_obj.suffix,
            "mime_type": mime_type,
            "supported": settings.is_supported_file(str(file_path_obj)),
            "modified": stat.st_mtime,
        }
    
    def is_available(self) -> bool:
        """Check if document processor is ready to use."""
        return DOCLING_AVAILABLE and self.converter is not None
