"""
Token management and semantic text slicing for Expo.
"""

import logging
import re
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class TokenLimits:
    """Token limits for different models."""
    mistral_7b: int = 4000
    mistral_latest: int = 4000
    llama2: int = 4000
    default: int = 4000


class TokenManager:
    """
    Manages token limits and provides semantic text slicing.
    """
    
    def __init__(self, model_name: str = "mistral-7b-v0-1-gguf:latest"):
        """
        Initialize token manager.
        
        Args:
            model_name: Name of the LLM model
        """
        self.model_name = model_name
        self.limits = TokenLimits()
        self.token_limit = self._get_token_limit()
        
    def _get_token_limit(self) -> int:
        """Get token limit for the current model."""
        model_lower = self.model_name.lower()
        
        if "mistral" in model_lower:
            return self.limits.mistral_7b
        elif "llama" in model_lower:
            return self.limits.llama2
        else:
            return self.limits.default
    
    def estimate_tokens(self, text: str) -> int:
        """
        Estimate token count for text.
        
        Uses a simple approximation: ~4 characters per token for English text.
        This is conservative and works well for most cases.
        
        Args:
            text: Text to estimate tokens for
            
        Returns:
            Estimated token count
        """
        if not text:
            return 0
        
        # Simple estimation: 4 characters per token (conservative)
        char_count = len(text)
        estimated_tokens = char_count // 4
        
        # Add some buffer for special tokens and formatting
        estimated_tokens = int(estimated_tokens * 1.1)
        
        return estimated_tokens
    
    def check_token_limit(self, text: str) -> Tuple[bool, int]:
        """
        Check if text exceeds token limit.
        
        Args:
            text: Text to check
            
        Returns:
            Tuple of (within_limit, estimated_tokens)
        """
        estimated_tokens = self.estimate_tokens(text)
        within_limit = estimated_tokens <= self.token_limit
        
        if not within_limit:
            logger.warning(
                f"⚠️ Text length: {estimated_tokens} tokens "
                f"(exceeds limit of {self.token_limit} for {self.model_name})"
            )
        
        return within_limit, estimated_tokens
    
    def semantic_slice(self, text: str, preserve_ratio: float = 0.8) -> str:
        """
        Intelligently slice text to fit within token limits while preserving meaning.
        
        Args:
            text: Text to slice
            preserve_ratio: Ratio of token limit to target (0.8 = 80% of limit)
            
        Returns:
            Sliced text that fits within limits
        """
        target_tokens = int(self.token_limit * preserve_ratio)
        current_tokens = self.estimate_tokens(text)
        
        if current_tokens <= target_tokens:
            return text
        
        logger.info(f"Applying semantic slicing: {current_tokens} → {target_tokens} tokens")
        
        # Calculate target length
        target_length = int(len(text) * (target_tokens / current_tokens))
        
        # Try different slicing strategies in order of preference
        strategies = [
            self._slice_by_sections,
            self._slice_by_paragraphs,
            self._slice_by_sentences,
            self._slice_by_lines,
            self._slice_by_characters
        ]
        
        for strategy in strategies:
            try:
                sliced_text = strategy(text, target_length)
                if self.estimate_tokens(sliced_text) <= target_tokens:
                    logger.info(f"Successfully sliced using {strategy.__name__}")
                    return sliced_text
            except Exception as e:
                logger.warning(f"Slicing strategy {strategy.__name__} failed: {e}")
                continue
        
        # Fallback: simple truncation
        logger.warning("All semantic slicing failed, using simple truncation")
        return text[:target_length]
    
    def _slice_by_sections(self, text: str, target_length: int) -> str:
        """Slice by document sections (headers, etc.)."""
        # Look for section markers
        sections = re.split(r'\n\s*(?=[A-Z][A-Za-z\s:]+\n|\d+\.|\*\*|##)', text)
        
        if len(sections) <= 1:
            raise ValueError("No clear sections found")
        
        # Keep most important sections (beginning and any with key terms)
        important_sections = []
        key_terms = ['location', 'date', 'schedule', 'group', 'zodiac', 'equipment', 'tide']
        
        # Always include first section
        if sections:
            important_sections.append(sections[0])
        
        # Add sections with key terms
        for section in sections[1:]:
            if any(term in section.lower() for term in key_terms):
                important_sections.append(section)
        
        # Join and check length
        result = '\n'.join(important_sections)
        if len(result) <= target_length:
            return result
        
        # If still too long, truncate last section
        if len(important_sections) > 1:
            truncated_last = important_sections[-1][:target_length - len('\n'.join(important_sections[:-1]))]
            return '\n'.join(important_sections[:-1] + [truncated_last])
        
        return result[:target_length]
    
    def _slice_by_paragraphs(self, text: str, target_length: int) -> str:
        """Slice by paragraphs, keeping most relevant ones."""
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        
        if len(paragraphs) <= 1:
            raise ValueError("No clear paragraphs found")
        
        # Score paragraphs by relevance
        scored_paragraphs = []
        key_terms = ['location', 'date', 'schedule', 'group', 'zodiac', 'equipment', 'tide', 'time']
        
        for i, para in enumerate(paragraphs):
            score = 0
            para_lower = para.lower()
            
            # Higher score for paragraphs with key terms
            for term in key_terms:
                score += para_lower.count(term)
            
            # Bonus for early paragraphs (likely more important)
            if i < 3:
                score += 2
            
            # Bonus for paragraphs with times
            if re.search(r'\d{1,2}:\d{2}', para):
                score += 1
            
            scored_paragraphs.append((score, para))
        
        # Sort by score (descending) and take best paragraphs that fit
        scored_paragraphs.sort(key=lambda x: x[0], reverse=True)
        
        selected_paragraphs = []
        current_length = 0
        
        for score, para in scored_paragraphs:
            if current_length + len(para) + 2 <= target_length:  # +2 for \n\n
                selected_paragraphs.append(para)
                current_length += len(para) + 2
            else:
                break
        
        return '\n\n'.join(selected_paragraphs)
    
    def _slice_by_sentences(self, text: str, target_length: int) -> str:
        """Slice by sentences, keeping most important ones."""
        # Simple sentence splitting
        sentences = re.split(r'[.!?]+\s+', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if len(sentences) <= 1:
            raise ValueError("No clear sentences found")
        
        # Take sentences from beginning until we reach target
        selected_sentences = []
        current_length = 0
        
        for sentence in sentences:
            if current_length + len(sentence) + 2 <= target_length:
                selected_sentences.append(sentence)
                current_length += len(sentence) + 2
            else:
                break
        
        return '. '.join(selected_sentences) + '.'
    
    def _slice_by_lines(self, text: str, target_length: int) -> str:
        """Slice by lines, keeping most important ones."""
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        if len(lines) <= 1:
            raise ValueError("No clear lines found")
        
        # Prioritize lines with key information
        key_terms = ['location', 'date', 'schedule', 'group', 'zodiac', 'equipment', 'tide']
        important_lines = []
        other_lines = []
        
        for line in lines:
            line_lower = line.lower()
            if any(term in line_lower for term in key_terms) or re.search(r'\d{1,2}:\d{2}', line):
                important_lines.append(line)
            else:
                other_lines.append(line)
        
        # Take important lines first, then others
        selected_lines = []
        current_length = 0
        
        for line in important_lines + other_lines:
            if current_length + len(line) + 1 <= target_length:
                selected_lines.append(line)
                current_length += len(line) + 1
            else:
                break
        
        return '\n'.join(selected_lines)
    
    def _slice_by_characters(self, text: str, target_length: int) -> str:
        """Simple character-based slicing (fallback)."""
        if len(text) <= target_length:
            return text
        
        # Try to cut at a word boundary
        truncated = text[:target_length]
        last_space = truncated.rfind(' ')
        
        if last_space > target_length * 0.8:  # If we can cut at a word boundary reasonably close
            return truncated[:last_space] + "..."
        
        return truncated + "..."
    
    def prepare_text_for_llm(self, text: str, context: str = "") -> str:
        """
        Prepare text for LLM processing with token limit checking and slicing.
        
        Args:
            text: Main text to process
            context: Additional context (prompt, etc.)
            
        Returns:
            Text prepared for LLM processing
        """
        # Calculate available space for main text
        context_tokens = self.estimate_tokens(context)
        available_tokens = self.token_limit - context_tokens - 100  # 100 token buffer
        
        if available_tokens <= 0:
            logger.error("Context too large, no space for main text")
            return ""
        
        # Check if main text fits
        text_tokens = self.estimate_tokens(text)
        
        if text_tokens <= available_tokens:
            logger.info(f"Text fits within limits: {text_tokens + context_tokens} total tokens")
            return text
        
        # Apply semantic slicing
        target_ratio = available_tokens / self.token_limit
        sliced_text = self.semantic_slice(text, preserve_ratio=target_ratio)
        
        final_tokens = self.estimate_tokens(sliced_text) + context_tokens
        logger.info(f"Text prepared for LLM: {final_tokens} total tokens (limit: {self.token_limit})")
        
        return sliced_text
