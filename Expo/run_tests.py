#!/usr/bin/env python3
"""
Test runner for Expo components.
"""

import sys
import unittest
import logging
from pathlib import Path

# Add Expo to path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging for tests
logging.basicConfig(level=logging.WARNING)


def run_component_tests():
    """Run individual component tests."""
    print("🧪 Running Component Tests...")
    print("=" * 50)
    
    try:
        from tests.test_components import (
            TestExpoSettings,
            TestDocumentProcessor, 
            TestJSONGenerator,
            TestOutputManager,
            TestExtractionAgent
        )
        
        # Create test suite
        suite = unittest.TestSuite()
        
        # Add component tests
        suite.addTest(unittest.makeSuite(TestExpoSettings))
        suite.addTest(unittest.makeSuite(TestDocumentProcessor))
        suite.addTest(unittest.makeSuite(TestJSONGenerator))
        suite.addTest(unittest.makeSuite(TestOutputManager))
        suite.addTest(unittest.makeSuite(TestExtractionAgent))
        
        # Run tests
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return result.wasSuccessful()
        
    except ImportError as e:
        print(f"❌ Failed to import component tests: {e}")
        return False


def run_integration_tests():
    """Run integration tests."""
    print("\n🔗 Running Integration Tests...")
    print("=" * 50)
    
    try:
        from tests.test_integration import TestExpoIntegration, TestExpoTools
        
        # Create test suite
        suite = unittest.TestSuite()
        
        # Add integration tests
        suite.addTest(unittest.makeSuite(TestExpoIntegration))
        suite.addTest(unittest.makeSuite(TestExpoTools))
        
        # Run tests
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return result.wasSuccessful()
        
    except ImportError as e:
        print(f"❌ Failed to import integration tests: {e}")
        return False


def test_imports():
    """Test that all Expo modules can be imported."""
    print("📦 Testing Module Imports...")
    print("=" * 50)
    
    modules_to_test = [
        "config.settings",
        "config.ollama_config", 
        "core.document_processor",
        "core.json_generator",
        "core.output_manager",
        "agents.extraction_agent",
        "tools.extraction_tools",
        "ui.file_picker",
    ]
    
    failed_imports = []
    
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n❌ Failed to import {len(failed_imports)} modules")
        return False
    else:
        print(f"\n✅ All {len(modules_to_test)} modules imported successfully")
        return True


def test_dependencies():
    """Test that required dependencies are available."""
    print("\n📚 Testing Dependencies...")
    print("=" * 50)
    
    dependencies = [
        ("json", "Built-in JSON support"),
        ("pathlib", "Built-in path handling"),
        ("tkinter", "GUI support"),
        ("docling", "Document processing"),
        ("langchain", "LangChain framework"),
        ("ollama", "Ollama integration"),
    ]
    
    missing_deps = []
    
    for dep, description in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep}: {description}")
        except ImportError:
            print(f"⚠️  {dep}: {description} (optional)")
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"\n⚠️  {len(missing_deps)} optional dependencies missing")
        print("Some features may not be available.")
    
    return True


def main():
    """Main test runner."""
    print("🚀 Expo Test Suite")
    print("=" * 50)
    
    all_passed = True
    
    # Test imports first
    if not test_imports():
        print("\n❌ Import tests failed. Cannot continue.")
        return 1
    
    # Test dependencies
    test_dependencies()
    
    # Run component tests
    if not run_component_tests():
        all_passed = False
    
    # Run integration tests
    if not run_integration_tests():
        all_passed = False
    
    # Summary
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed!")
        print("\nExpo is ready to use:")
        print("  • GUI: python -m Expo.main")
        print("  • CLI: python -m Expo.cli document.pdf")
        return 0
    else:
        print("❌ Some tests failed.")
        print("Please check the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
