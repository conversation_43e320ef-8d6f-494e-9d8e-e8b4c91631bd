"""
Main application entry point for Expo.
"""

import logging
import sys
from pathlib import Path
from typing import List

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import Expo modules
from core.document_processor import DocumentProcessor
from core.json_generator import JSONGenerator
from agents.extraction_agent import ExtractionAgent
from ui.file_picker import FilePicker
from config.settings import settings


class ExpoApp:
    """
    Main Expo application for processing expedition documents.
    """
    
    def __init__(self):
        """Initialize the Expo application."""
        self.document_processor = DocumentProcessor()
        self.extraction_agent = ExtractionAgent()
        self.json_generator = JSONGenerator()
        self.file_picker = FilePicker(on_files_selected=self.process_files)
        
        logger.info("Expo application initialized")
    
    def run(self):
        """Run the main application."""
        logger.info("Starting Expo - Expedition Document Processor")
        
        # Check if components are available
        if not self.document_processor.is_available():
            logger.error("Document processor not available. Please install docling.")
            sys.exit(1)
        
        if not self.extraction_agent.is_available():
            logger.warning("Lang<PERSON>hain agent not available. Will use fallback extraction.")
        
        try:
            # Select files
            logger.info("Opening file picker...")
            selected_files = self.file_picker.select_files()
            
            if not selected_files:
                logger.info("No files selected. Exiting.")
                return
            
            logger.info(f"Selected {len(selected_files)} files for processing")
            
            # Show processing window
            processing_window = self.file_picker.show_processing_window(selected_files)
            
            # Start GUI event loop
            if processing_window:
                processing_window.mainloop()
            
        except KeyboardInterrupt:
            logger.info("Application interrupted by user")
        except Exception as e:
            logger.error(f"Application error: {e}")
        finally:
            self.cleanup()
    
    def process_files(self, file_paths: List[str]) -> List[str]:
        """
        Process a list of expedition document files.
        
        Args:
            file_paths: List of file paths to process
            
        Returns:
            List of output JSON file paths
        """
        results = []
        
        for i, file_path in enumerate(file_paths):
            try:
                logger.info(f"Processing file {i+1}/{len(file_paths)}: {Path(file_path).name}")
                
                # Update progress
                self.file_picker.update_progress(
                    i + 1, 
                    len(file_paths), 
                    f"Processing: {Path(file_path).name}"
                )
                
                # Extract text from document
                logger.info("Extracting text from document...")
                text_content = self.document_processor.extract_text(file_path)
                
                if not text_content:
                    logger.warning(f"No text extracted from: {file_path}")
                    continue
                
                logger.info(f"Extracted {len(text_content)} characters of text")
                
                # Extract entities using agent
                logger.info("Extracting entities with LangChain agent...")
                extracted_entities = self.extraction_agent.extract_entities(text_content)
                
                if not extracted_entities:
                    logger.warning(f"No entities extracted from: {file_path}")
                    continue
                
                logger.info(f"Extracted entities: {list(extracted_entities.keys())}")
                
                # Generate JSON template
                logger.info("Generating JSON template...")
                json_template = self.json_generator.create_template(extracted_entities)
                
                # Validate template
                validation_errors = self.json_generator.validate_template(json_template)
                if validation_errors:
                    logger.warning(f"Template validation warnings: {validation_errors}")
                
                # Save output
                logger.info("Saving JSON output...")
                output_path = self.json_generator.save_output(json_template, file_path)
                results.append(output_path)
                
                logger.info(f"Successfully processed: {Path(file_path).name} -> {Path(output_path).name}")
                
            except Exception as e:
                logger.error(f"Error processing {file_path}: {e}")
                continue
        
        # Show results
        if results:
            logger.info(f"Processing completed. Generated {len(results)} JSON files.")
            self.file_picker.show_results(results)
        else:
            logger.warning("No files were processed successfully.")
        
        return results
    
    def process_single_file(self, file_path: str) -> str:
        """
        Process a single expedition document file.
        
        Args:
            file_path: Path to the file to process
            
        Returns:
            Path to output JSON file
        """
        results = self.process_files([file_path])
        return results[0] if results else None
    
    def cleanup(self):
        """Clean up application resources."""
        logger.info("Cleaning up application resources...")
        if self.file_picker:
            self.file_picker.cleanup()


def main():
    """Main entry point for the application."""
    try:
        app = ExpoApp()
        app.run()
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
