# Expo - Modular Expedition Planner

A clean, standalone rebuild of the expedition planner focused on core functionality:

## Architecture

```
Expo/
├── core/               # Core processing modules
│   ├── document_processor.py    # Document ingestion & Docling extraction
│   └── json_generator.py        # JSON template generation
├── agents/             # LangChain agents
│   └── extraction_agent.py      # Entity extraction agent
├── tools/              # LangChain tools
│   ├── docling_tool.py          # Docling integration tool
│   └── template_tool.py         # Template generation tool
├── config/             # Configuration
│   ├── settings.py              # App settings
│   └── ollama_config.py         # Ollama/LLM configuration
├── ui/                 # User interface
│   └── file_picker.py           # File selection interface
├── output/             # Output directory (local)
└── tests/              # Test modules

```

## Features

### Phase 1 - Core Functionality
- ✅ Document ingestion (PDF, DOCX, DOC, TXT)
- ✅ Docling-based text extraction
- ✅ LangChain agent integration with Ollama
- ✅ JSON template generation
- ✅ Minimal file picker UI
- ✅ Automatic output saving

### JSON Template Structure
```json
{
  "dayNumber": 2,
  "weekday": "Friday", 
  "date": "2024-07-12",
  "location": "The Lacepedes",
  "utcOffset": "+00:00",
  "notes": "Equipment and setup notes",
  "zodiacs": 8,
  "twins": 1,
  "activityType": "2h Zodiac Cruise",
  "groups": [...],
  "schedule": [...],
  "tides": [...],
  "groupOrder": ["Yellow", "Blue", "Red", "Green"],
  "zodiacDrivers": [...],
  "landingGuides": {...},
  "nextDayLocation": "Next Location",
  "sunTimes": {...}
}
```

## Usage

```python
from Expo import DocumentProcessor, ExtractionAgent, JSONGenerator, FilePicker

# Initialize components
processor = DocumentProcessor()
agent = ExtractionAgent()
generator = JSONGenerator()
picker = FilePicker()

# Select and process files
files = picker.select_files()
for file_path in files:
    # Extract text
    text = processor.extract_text(file_path)
    
    # Extract entities
    entities = agent.extract_entities(text)
    
    # Generate JSON
    json_output = generator.create_template(entities)
    
    # Save output
    generator.save_output(json_output, file_path)
```

## Dependencies

- docling: Document extraction
- langchain: Agent framework
- ollama: Local LLM integration
- tkinter: File picker UI (built-in)

## Output

JSON files are automatically saved to `~/Desktop/output/` with naming convention:
- `location-date.json` (e.g., `lacepedes-2024-07-12.json`)
