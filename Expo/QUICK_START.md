# ⚡ Expo Quick Start Guide

Get up and running with <PERSON> in 5 minutes!

## 🚀 One-Command Setup (Recommended)

If you have the install script:

```bash
cd Expo
chmod +x install.sh
./install.sh
```

This automatically:
- ✅ Installs Ollama
- ✅ Downloads the AI model
- ✅ Sets up Python environment
- ✅ Installs all dependencies
- ✅ Runs verification tests

## 📋 Manual Setup (Step-by-Step)

### 1. Install Ollama (AI Engine)

**macOS/Linux:**
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

**Windows:**
Download from [ollama.ai/download](https://ollama.ai/download)

### 2. Download AI Model

```bash
ollama pull mistral-7b-v0-1-gguf:latest
```
*This downloads ~4GB - ensure good internet*

### 3. Setup Python Environment

```bash
cd Expo
python -m venv expo_env

# Activate environment:
source expo_env/bin/activate  # macOS/Linux
expo_env\Scripts\activate     # Windows

pip install -r requirements.txt
```

### 4. Test Installation

```bash
python test_agent_only.py
```

**Expected:** ✅ Agent extraction successful!

## 🎯 Start Processing Documents

### GUI (Easiest)
```bash
python simple_gui.py
```
- Drag & drop your expedition documents
- Click "Process Files"
- View results in real-time

### Command Line
```bash
python cli.py your_document.pdf
```

## 📊 What You Get

Expo extracts structured data from expedition documents:

```json
{
  "date": "2024-07-15",
  "location": "Test Island", 
  "activities": [{
    "type": "Zodiac Cruise",
    "zodiacCount": 6,
    "groups": [
      {"groupName": "Yellow", "startTime": "08:00"},
      {"groupName": "Blue", "startTime": "10:00"}
    ]
  }],
  "tides": [
    {"time": "03:00", "height": 1.5, "type": "low"}
  ],
  "metadata": {
    "confidenceScore": 0.92
  }
}
```

## 🔧 Quick Troubleshooting

**❌ "ollama command not found"**
```bash
# Restart terminal or add to PATH:
export PATH="/usr/local/bin:$PATH"
```

**❌ "Model not found"**
```bash
ollama pull mistral-7b-v0-1-gguf:latest
ollama list  # Verify it's there
```

**❌ "Virtual environment issues"**
```bash
# Make sure you see (expo_env) in prompt
source expo_env/bin/activate
```

**❌ "Slow processing"**
- Ensure 4GB+ RAM available
- Use smaller documents for testing
- Close other applications

## 📁 Output Location

JSON files are saved to:
- **Default:** `~/Desktop/output/`
- **Format:** `location-date.json`
- **Example:** `test-island-2024-07-15.json`

## 🎓 Next Steps

1. **Process your expedition documents** (PDF works best)
2. **Customize settings** in `config/` folder
3. **Explore batch processing** with multiple files
4. **Check README.md** for advanced features

## 🆘 Need Help?

1. Run `python quick_test.py` to verify setup
2. Check logs for detailed error messages
3. See README.md for comprehensive troubleshooting
4. Ensure Ollama is running: `ollama list`

## ✨ Features

- 🔍 **AI-Powered**: LangChain agents with local LLMs
- 📄 **Multi-Format**: PDF, DOCX, DOC, TXT support
- 🎯 **Structured Output**: Professional JSON templates
- 🛡️ **Reliable**: Fallback extraction when AI fails
- ⚡ **Fast**: 15-60 seconds per document
- 🔒 **Private**: All processing happens locally

Ready to extract expedition data intelligently! 🗺️⛵
