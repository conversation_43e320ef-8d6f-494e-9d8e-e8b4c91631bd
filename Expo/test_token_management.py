#!/usr/bin/env python3
"""
Test token management and semantic slicing functionality.
"""

import sys
from core.token_manager import To<PERSON><PERSON><PERSON><PERSON>

def test_token_management():
    """Test the token management system."""
    
    print("🧪 Testing Token Management System")
    print("=" * 50)
    
    # Initialize token manager
    token_manager = TokenManager("mistral-7b-v0-1-gguf:latest")
    print(f"Token limit: {token_manager.token_limit}")
    
    # Test 1: Small text (should pass)
    print("\n📏 Test 1: Small Text")
    small_text = "Location: Test Island\nDate: 2024-07-15\nEquipment: 6 zodiacs"
    within_limit, tokens = token_manager.check_token_limit(small_text)
    print(f"Text: {len(small_text)} chars, {tokens} tokens")
    print(f"Within limit: {within_limit}")
    
    # Test 2: Large text (should trigger slicing)
    print("\n📏 Test 2: Large Text")
    large_text = """
    EXPEDITION PLANNING DOCUMENT - COMPREHENSIVE OPERATIONS MANUAL
    
    Location: Iheyajima – Maedomari- Yonezaki
    Date: 17th March 2024
    
    EQUIPMENT MANIFEST:
    - 4 zodiacs (mach5) - high-speed inflatable boats for passenger transport
    - 1 ladder - aluminum boarding ladder for ship access
    - 1 Jason cradle - specialized equipment cradle for underwater operations
    - 6 life jackets - standard flotation devices for passenger safety
    - 2 emergency radios - waterproof communication devices
    - 1 first aid kit - comprehensive medical supplies for emergencies
    - 3 waterproof bags - equipment protection during water operations
    
    DETAILED SCHEDULE AND OPERATIONS:
    
    MORNING OPERATIONS:
    8:00 Schedule arrival time at Iheyajima marina
    8:00 Scout boat deployment with crew: Ryo (captain), Xav (navigator), Katsu (safety officer), Eli (communications)
    8:30 Equipment drop including Arien (equipment specialist), Daniel (marine biologist), Phil (photographer)
    8:30 ALL Team disembark and stand by at marina facility for passenger operations
    8:50 Life jacket distribution coordinated by Jacquie and Nico G (safety coordinators)
    9:00 Disembarkation Red group - first passenger group for morning expedition
    9:15 Disembarkation Green group - second passenger group with specialized equipment
    9:30 Disembarkation Blue Group - final morning passenger group
    9:45 Crew departure for equipment setup and safety monitoring
    
    OPERATIONAL NOTES:
    Keep 2 zodiacs for continuous shuttle service between ship and shore
    Other drivers can attach zodiac equipment but must remain on standby aboard vessel 71
    Maintain constant radio communication between all zodiac operators
    Weather monitoring is critical - abort operations if conditions deteriorate
    
    12:45 Last zodiac return from morning operations
    13:15 Schedule departure from Iheyajima marina
    
    AFTERNOON OPERATIONS:
    14:30 Schedule arrival time for afternoon session
    14:30 Zodiac deployment with crew: Nico G (lead), Rao (assistant), Pierre (safety), Nico T (backup)
    14:30 ALL team stand by for afternoon passenger operations
    15:00 Disembarkation Red group - afternoon expedition begins
    15:15 Disembarkation Green group - specialized afternoon activities
    15:30 Disembarkation Blue Group - final afternoon passenger group
    
    SPECIAL ARRANGEMENTS:
    French speaker on bus: Kento (bilingual guide for international passengers)
    15:45 Crew deployment for afternoon operations
    15:45 1 Zodiac with ladder designated as safety zodiac: Nico T (rotation available on request)
    15:45 2 Zodiacs designated for continuous shuttle operations
    
    17:00 Last zodiac return from afternoon operations
    18:00 Briefing at Zamami theatre (2 languages - English and Japanese)
    19:00 Gala dinner and cultural presentation
    
    TIDE INFORMATION:
    High tide: 11:09 (1.5m height) - optimal for zodiac operations
    Low tide: 05:01 (1.1m height) - caution required for shallow areas
    Evening tide: 19:09 (0.5m height) - monitor carefully for return operations
    
    SAFETY PROTOCOLS:
    - All passengers must wear life jackets during zodiac operations
    - Maximum 8 passengers per zodiac (excluding crew)
    - Weather conditions must be monitored continuously
    - Emergency procedures must be briefed to all passengers
    - Radio check every 30 minutes during operations
    - First aid kit must be present on each zodiac
    
    EMERGENCY CONTACTS:
    - Coast Guard: Emergency frequency 156.8 MHz
    - Local marina: +81-98-987-2345
    - Medical facility: +81-98-987-1234
    - Expedition coordinator: +81-90-1234-5678
    
    EQUIPMENT CHECKLIST:
    □ Zodiacs fueled and safety checked
    □ Life jackets inspected and counted
    □ Radio equipment tested
    □ First aid kits stocked
    □ Emergency flares available
    □ Weather monitoring equipment operational
    □ Passenger manifest completed
    □ Crew assignments confirmed
    
    This comprehensive operations manual ensures safe and efficient expedition operations at Iheyajima.
    All crew members must be familiar with these procedures before operations begin.
    Regular safety drills and equipment checks are mandatory.
    """ * 3  # Make it even larger
    
    within_limit, tokens = token_manager.check_token_limit(large_text)
    print(f"Text: {len(large_text)} chars, {tokens} tokens")
    print(f"Within limit: {within_limit}")
    
    if not within_limit:
        print("\n🔧 Applying Semantic Slicing...")
        sliced_text = token_manager.semantic_slice(large_text)
        sliced_within_limit, sliced_tokens = token_manager.check_token_limit(sliced_text)
        print(f"Sliced text: {len(sliced_text)} chars, {sliced_tokens} tokens")
        print(f"Sliced within limit: {sliced_within_limit}")
        
        # Show first 200 chars of sliced text
        print(f"\nSliced text preview:")
        print(f"'{sliced_text[:200]}...'")
    
    # Test 3: Prepare text for LLM with context
    print("\n🤖 Test 3: Prepare for LLM")
    context = "Extract expedition information from this document and return JSON:"
    prepared_text = token_manager.prepare_text_for_llm(large_text, context)
    final_tokens = token_manager.estimate_tokens(prepared_text + context)
    print(f"Context: {token_manager.estimate_tokens(context)} tokens")
    print(f"Prepared text: {token_manager.estimate_tokens(prepared_text)} tokens")
    print(f"Total: {final_tokens} tokens")
    print(f"Within limit: {final_tokens <= token_manager.token_limit}")
    
    print("\n" + "=" * 50)
    print("✅ Token Management Test Complete!")
    
    # Summary
    improvements = [
        "✅ Token estimation working",
        "✅ Limit checking functional", 
        "✅ Semantic slicing operational",
        "✅ LLM preparation ready"
    ]
    
    print(f"\n🎉 Key Features:")
    for improvement in improvements:
        print(f"  {improvement}")
    
    return True

if __name__ == "__main__":
    try:
        success = test_token_management()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
