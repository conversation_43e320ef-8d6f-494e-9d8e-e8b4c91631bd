"""
File picker interface for Expo using tkinter.
"""

import logging
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from pathlib import Path
from typing import List, Optional, Callable
import threading

from ..config.settings import settings

logger = logging.getLogger(__name__)


class FilePicker:
    """
    Simple file picker interface for selecting expedition documents.
    """
    
    def __init__(self, on_files_selected: Optional[Callable] = None):
        """
        Initialize the file picker.
        
        Args:
            on_files_selected: Callback function when files are selected
        """
        self.on_files_selected = on_files_selected
        self.selected_files = []
        self.root = None
        self.progress_var = None
        self.status_var = None
        
    def select_files(self) -> List[str]:
        """
        Open file picker dialog to select expedition documents.
        
        Returns:
            List of selected file paths
        """
        try:
            # Create root window if not exists
            if not self.root:
                self.root = tk.Tk()
                self.root.withdraw()  # Hide main window
            
            # File dialog
            file_types = [
                ("All Supported", "*.pdf;*.docx;*.doc;*.txt"),
                ("PDF files", "*.pdf"),
                ("Word documents", "*.docx;*.doc"),
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
            
            files = filedialog.askopenfilenames(
                title="Select Expedition Documents",
                filetypes=file_types,
                initialdir=str(Path.home())
            )
            
            # Filter supported files
            supported_files = []
            unsupported_files = []
            
            for file_path in files:
                if settings.is_supported_file(file_path):
                    supported_files.append(file_path)
                else:
                    unsupported_files.append(file_path)
            
            # Show warning for unsupported files
            if unsupported_files:
                unsupported_names = [Path(f).name for f in unsupported_files]
                messagebox.showwarning(
                    "Unsupported Files",
                    f"The following files are not supported and will be skipped:\n\n" +
                    "\n".join(unsupported_names)
                )
            
            self.selected_files = supported_files
            logger.info(f"Selected {len(supported_files)} supported files")
            
            return supported_files
            
        except Exception as e:
            logger.error(f"Error in file selection: {e}")
            if self.root:
                messagebox.showerror("Error", f"Failed to select files: {e}")
            return []
    
    def show_processing_window(self, files: List[str]) -> tk.Toplevel:
        """
        Show processing window with progress bar.
        
        Args:
            files: List of files being processed
            
        Returns:
            Processing window
        """
        if not self.root:
            self.root = tk.Tk()
            self.root.withdraw()
        
        # Create processing window
        window = tk.Toplevel(self.root)
        window.title("Processing Expedition Documents")
        window.geometry("500x300")
        window.resizable(False, False)
        
        # Center window
        window.transient(self.root)
        window.grab_set()
        
        # Main frame
        main_frame = ttk.Frame(window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(
            main_frame, 
            text="Processing Expedition Documents",
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # File list
        list_frame = ttk.LabelFrame(main_frame, text="Selected Files", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # Scrollable file list
        list_scroll = tk.Scrollbar(list_frame)
        list_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        file_listbox = tk.Listbox(
            list_frame,
            yscrollcommand=list_scroll.set,
            height=6
        )
        file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        list_scroll.config(command=file_listbox.yview)
        
        # Add files to list
        for file_path in files:
            file_listbox.insert(tk.END, Path(file_path).name)
        
        # Progress section
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=400
        )
        progress_bar.pack(fill=tk.X, pady=(0, 10))
        
        # Status label
        self.status_var = tk.StringVar()
        self.status_var.set("Ready to process...")
        status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        status_label.pack()
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        # Start button
        start_button = ttk.Button(
            button_frame,
            text="Start Processing",
            command=lambda: self._start_processing(files, window)
        )
        start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        # Cancel button
        cancel_button = ttk.Button(
            button_frame,
            text="Cancel",
            command=window.destroy
        )
        cancel_button.pack(side=tk.LEFT)
        
        return window
    
    def _start_processing(self, files: List[str], window: tk.Toplevel):
        """Start processing files in background thread."""
        if self.on_files_selected:
            # Disable buttons during processing
            for child in window.winfo_children():
                if isinstance(child, ttk.Frame):
                    for button in child.winfo_children():
                        if isinstance(button, ttk.Button):
                            button.config(state="disabled")
            
            # Start processing in background
            thread = threading.Thread(
                target=self._process_files_thread,
                args=(files, window)
            )
            thread.daemon = True
            thread.start()
    
    def _process_files_thread(self, files: List[str], window: tk.Toplevel):
        """Process files in background thread."""
        try:
            total_files = len(files)
            
            for i, file_path in enumerate(files):
                # Update status
                self.status_var.set(f"Processing: {Path(file_path).name}")
                
                # Update progress
                progress = (i / total_files) * 100
                self.progress_var.set(progress)
                
                # Process file (call callback)
                if self.on_files_selected:
                    self.on_files_selected([file_path])
                
                # Small delay to show progress
                window.after(100)
            
            # Complete
            self.progress_var.set(100)
            self.status_var.set(f"Completed! Processed {total_files} files.")
            
            # Show completion message
            window.after(1000, lambda: messagebox.showinfo(
                "Processing Complete",
                f"Successfully processed {total_files} files.\n\n"
                f"Output saved to: {settings.output_directory}"
            ))
            
            # Close window after delay
            window.after(2000, window.destroy)
            
        except Exception as e:
            logger.error(f"Error processing files: {e}")
            self.status_var.set(f"Error: {e}")
            window.after(100, lambda: messagebox.showerror("Processing Error", str(e)))
    
    def update_progress(self, current: int, total: int, status: str = ""):
        """
        Update progress bar and status.
        
        Args:
            current: Current file number
            total: Total number of files
            status: Status message
        """
        if self.progress_var:
            progress = (current / total) * 100 if total > 0 else 0
            self.progress_var.set(progress)
        
        if self.status_var and status:
            self.status_var.set(status)
    
    def show_results(self, results: List[str]):
        """
        Show processing results.
        
        Args:
            results: List of output file paths
        """
        if not results:
            messagebox.showinfo("No Results", "No files were processed successfully.")
            return
        
        message = f"Processing completed!\n\n"
        message += f"Generated {len(results)} JSON files:\n\n"
        
        for result in results[:5]:  # Show first 5 results
            message += f"• {Path(result).name}\n"
        
        if len(results) > 5:
            message += f"• ... and {len(results) - 5} more files\n"
        
        message += f"\nOutput directory: {settings.output_directory}"
        
        messagebox.showinfo("Processing Complete", message)
    
    def cleanup(self):
        """Clean up UI resources."""
        if self.root:
            self.root.quit()
            self.root.destroy()
            self.root = None
