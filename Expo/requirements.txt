# Expo - Expedition Document Processor
# Core dependencies for standalone operation

# Document processing
docling>=2.38.1
pymupdf>=1.23.0,<2.0.0
python-docx>=1.1.2,<2.0.0
pytesseract>=0.3.10,<1.0.0
pillow>=10.0.0,<12.0.0

# LangChain and AI
langchain>=0.1.0,<1.0.0
langchain-community>=0.0.20,<1.0.0
langchain-core>=0.1.0,<1.0.0
langchain-ollama>=0.1.0,<1.0.0
ollama>=0.1.0,<1.0.0

# Data processing
pydantic>=2.0.0,<3.0.0
python-dateutil>=2.8.2

# File handling
filetype>=1.2.0,<2.0.0
python-magic>=0.4.27

# Utilities
requests>=2.32.2,<3.0.0
certifi>=2024.7.4
